# 基于PTDF的风光发电过载调度策略模型

## 引言：风光发电过载场景及调度意义

随着可再生能源比例的增加，风电、光伏（==大发==）等情况下导致的线路过载问题日益凸显。当风力条件良好或日照充足时，风电场或光伏电站大发电会导致送出线路负载超过安全阈值，需要调度中心及时干预。本文建立基于PTDF的风光发电厂线路过载调度策略模型，用于指导调度中心快速响应并解决过载问题。

**调度的重要性：**

- **保障电网安全**：过载线路如不及时处理，可能导致线路跳闸，引发更严重的连锁故障
- **最大化可再生能源消纳**：通过精准调度，在确保电网安全的前提下最大限度利用风光资源
- **提高系统经济性**：采用优化的调度策略，最小化发电站的出力损失

## 风光并网线路过载检测模型

### 线路负载率计算

线路负载率是衡量线路过载程度的关键指标，定义为线路实际传输的视在功率与其额定容量之比：

$$
LR_l(t) = \frac{P_{line,l}(t)}{P_{rated,l}} \times 100\%
$$

其中：
- $LR_l(t)$ 表示线路 $l$ 在时间 $t$ 的负载率（百分比）
- $P_{line,l}(t)$ 表示线路 $l$ 在时间 $t$ 的视在功率（MVA）
- $P_{rated,l}$ 表示线路 $l$ 的额定容量（MVA）

当 $LR_l(t) > 100\%$ 时，表示线路处于过载状态，需要采取调度措施。

### 新能源送出线路视在功率计算

新能源（风电、光伏）送出线路的视在功率计算如下：

$$
S_{line,l}(t) = \sqrt{P_{line,l}^2(t) + Q_{line,l}^2(t)}
$$

其中：
- $P_{line,l}(t)$ 为线路 $l$ 在时间 $t$ 的有功功率
- $Q_{line,l}(t)$ 为线路 $l$ 在时间 $t$ 的无功功率

### 过载检测阈值（==明确==）

过载一般是指某两节点之间的某一条输电线路过载

考虑到线路短时过载能力和测量误差，设定分级过载检测阈值：

$$
LR_{threshold} = \begin{cases}
100\% + \delta_1, & \text{轻度过载} \\
100\% + \delta_2, & \text{中度过载} \\
100\% + \delta_3, & \text{重度过载}
\end{cases}
$$

其中 $\delta_1 < \delta_2 < \delta_3$ 为不同级别的裕度系数，通常取 $\delta_1 = 5\%$, $\delta_2 = 10\%$, $\delta_3 = 15\%$。

当 $LR_l(t) > 100\% + \delta_1$ 时，触发过载调度流程。

![image-20250808163213623](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250808163213623.png)

## 基于PTDF的风光发电过载调度模型

### PTDF基本概念与意义

**整体评估所有过载线路**

**实际调度是一个全局问题**，需要兼顾**所有过载线路**的安全，不能只盯着某一条线路调度；

PTDF（Power Transfer Distribution Factor，功率传输分布因子）是描述**电网中某条线路的功率潮流对某个节点注入功率变化的灵敏度**。PTDF矩阵是进行精确过载调度的核心技术基础。（==节点是什么==）

==PTDF 是作用在“单条输电线路”上的灵敏度指标，不能直接定义在“多个线路构成的路径”上。==

对于由n个节点和m条线路组成的电网，PTDF矩阵维度为m×n：

$$
PTDF = \begin{bmatrix}
PTDF_{1,1} & PTDF_{1,2} & \cdots & PTDF_{1,n} \\
PTDF_{2,1} & PTDF_{2,2} & \cdots & PTDF_{2,n} \\
\vdots & \vdots & \ddots & \vdots \\
PTDF_{m,1} & PTDF_{m,2} & \cdots & PTDF_{m,n}
\end{bmatrix}
$$

其中，$PTDF_{l,s}$表示节点s的功率注入变化对线路l的功率流影响系数：

$$
PTDF_{l,s} = \frac{\Delta S_l}{\Delta P_s}
$$

这里：
- $\Delta S_l$ 表示线路l的视在功率变化量（MVA）
- $\Delta P_s$ 表示节点s的有功注入功率变化量（MW）

PTDF值的物理意义是：==节点s注入功率增加1MW时，线路l的功率流增加$PTDF_{l,s}$MVA。==

### PTDF矩阵计算方法

实际应用中，PTDF矩阵主要通过以下两种方法计算：

#### 直流潮流法（简化法）

基于直流潮流模型的简化假设（忽略线路电阻，假设节点电压恒定，相角差较小），PTDF矩阵计算公式为：

$$
PTDF = [X_d]^{-1} \cdot A \cdot [B']
$$

其中：
- $X_d$ 是线路电抗对角矩阵
- $A$ 是线路-节点关联矩阵
- $B'$ 是节点导纳矩阵的广义逆矩阵

#### 交流潮流敏感度法（精确法）

考虑交流潮流的全部特性，使用雅可比矩阵求解：（==确认一下==）


$$
PTDF_{l,s} = \frac{\partial S_l}{\partial P_s} = \frac{\partial S_l}{\partial \theta} \cdot \frac{\partial \theta}{\partial P_s} + \frac{\partial S_l}{\partial V} \cdot \frac{\partial V}{\partial P_s}
$$

计算步骤：

1. **计算初始潮流**：求解电网初始状态下的节点电压和相角
2. **形成雅可比矩阵$J$**：构建电力系统（==状态变量（是电流变化吗？还是有很多？）==）与功率注入的敏感度矩阵（==3,4是否重复==）
3. **计算功率注入敏感度**：求解功率注入对节点电压和相角的影响
4. **计算潮流敏感度**：求解线路潮流对节点电压和相角的敏感度
5. **组合得到PTDF值**：通过链式法则计算综合敏感度

### 基于PTDF的调度目标与约束

风光发电过载调度的主要目标是通过调整发电站出力，使过载线路的负载率降至安全水平，同时尽量减小对==发电站发电量==的影响：

$$
\min \sum_{s=1}^{S} C_s \cdot \Delta P_s^{down}
$$

其中：
- $C_s$ 为发电站 $s$ 的调整成本系数或优先级
- $\Delta P_s^{down}$ 为发电站 $s$ 需要降低的出力(风机切断，光伏设置逆变器)

约束条件：对于所有处于过载状态的线路，其负载率必须被调度到不超过安全阈值：

$$
s.t. \quad LR_l(t) \leq LR_{safe} \quad \forall l \in L_{overload}
$$

其中：
- $LR_{safe}$ 为安全负载率，通常取95%
- $L_{overload}$ 为过载线路集合

### 基于PTDF的调度流程

PTDF调度流程是一个系统性的过程，包含以下关键步骤：

1. **电网数据准备**：
   - 收集电网拓扑结构信息
   - 获取线路参数和额定容量
   - 记录发电节点和负荷节点信息

2. **PTDF矩阵计算**：
   - 根据电网结构和参数计算完整PTDF矩阵
   - 验证PTDF矩阵准确性

3. **过载线路识别**：
   - 实时监测线路负载率
   - 识别负载率超过阈值的线路

4. **候选线路集合筛选**：
   - 形成候选调整集合：$S_l = \{s | |PTDF_{l,s}| > \varepsilon\}$
   - （==候选集合+AI==）
   - 其中$\varepsilon$是预设的PTDF阈值，用于筛选对过载线路有显著影响的节点
   
5. **调整量计算**：
   - 基于PTDF值和当前出力计算各发电站的调整量

6. **调度指令执行**：
   - 向相关发电站下发调度指令
   - 监测调度效果，必要时进行二次调整

### 基于PTDF的出力调整量计算

发电站 $s$ 需要减少的出力计算公式：

$$
\Delta P_s^{down} = \alpha_s \cdot \frac{(LR_l(t) - LR_{safe}) \cdot S_{rated,l}}{PTDF_{l,s}} \cdot \frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}
$$

其中：
- $\alpha_s$ 为发电站 $s$ 的调整系数，反映其调节优先级（==+AI==）
- $(LR_l(t) - LR_{safe}) \cdot S_{rated,l}$ 表示需要减少的线路传输功率
- $PTDF_{l,s}$ 表示发电站连接节点对线路的影响系数
- PTDF 大 → 调整效率高 → 所需调整量小。调整功率量与PTDF成反比。
- $\frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}$ 表示按发电站当前出力比例分配削减量
- $P_s(t)$是发电站$s$的功率

**公式组成部分详解**

1. **过载程度量化**：$(LR_l(t) - LR_{safe}) \cdot S_{rated,l}$
   - 这部分计算线路实际需要减少的功率量（MVA）
   - 例如：线路负载率为115%，安全阈值为95%，额定容量为300MVA
   - 需减少功率 = (115% - 95%) × 300MVA = 60MVA

2. **功率转换系数**：$\frac{1}{PTDF_{l,s}}$
   - PTDF值表示节点功率变化对线路功率的影响系数
   - 除以PTDF将"线路上需减少的功率"转换为"节点上需减少的功率"
   - ==对于PTDF值高的节点，同样的节点功率调整能产生更大的线路功率变化==
   - 例如：PTDF值为0.75的发电站，需调整80MW才能使线路功率减少60MVA (60÷0.75=80)

3. **负担分配因子**：$\frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}$
   - 这部分实现了按发电站出力比例分配调整责任
   - 出力较大的发电站承担更多调整量，体现公平性
   - 例如：发电站A出力300MW，集合内总出力为750MW，则分担比例为40%

4. **调整系数**：
   - 用于微调各发电站的调整比例，引入灵活性
   - 可基于经济性、技术特性、环保价值等因素设定
   - 例如：对灵活性高的风电场可设α=1.2，对光伏电站可设α=0.9

该公式巧妙地结合了两个关键因素：
- **技术效率**：通过PTDF值反映调整效率，优先利用高效率节点
- **公平分配**：通过出力比例分配调整负担，避免单一发电站承担全部责任。

### 发电站调整方案实施方式

根据实际需求，发电站调整出力的实施方式有多种：

1. **手动控制方式**：调度中心下发指令要求发电站降低出力，发电站自行决定具体的实施方案
2. **AGC远程控制方式**：调度中心通过AGC系统直接向发电站下发遥控指令

发电站内部出力调整计算：

**风电场**：计算需要切除的风机数量
$$
N_{cut,wind} = \left\lceil \frac{\Delta P_w^{down}}{P_{unit,wind}} \right\rceil
$$

**光伏电站**：设定逆变器总输出功率上限
$$
P_{pv,new\_setpoint} = P_{pv,current} - \Delta P_{pv}^{down}
$$

## 基于PTDF的风光发电过载调度案例分析

### 复杂电网单线路过载调度案例

考虑一个区域电网中的单线路过载问题，该区域包含4个发电节点（2个风电场、2个光伏电站）和1条主要过载线路：

**发电节点信息**：

- 风电场A：连接节点1，装机容量350MW，当前出力320MW，包含140台单机容量2.5MW的风机
- 风电场B：连接节点2，装机容量250MW，当前出力230MW，包含92台单机容量2.5MW的风机
- 光伏电站C：连接节点3，装机容量200MW，当前出力180MW，逆变器效率98%
- 光伏电站D：连接节点4，装机容量150MW，当前出力130MW，逆变器效率97%

**线路信息**：

- 线路L1：节点1→节点5→节点6，额定容量400MVA，当前负载率118%，安全负载率95%
- 线路参数：长度85公里，电阻0.028Ω/km，电抗0.315Ω/km，电容236nF/km

**PTDF值**：
基于详细的电网潮流计算，得到各发电站对线路L1的PTDF值：

- 风电场A对线路L1的PTDF值：0.72
- 风电场B对线路L1的PTDF值：0.38
- 光伏电站C对线路L1的PTDF值：0.25
- 光伏电站D对线路L1的PTDF值：0.12

### 详细调度计算过程

#### 线路过载分析

当前线路L1负载率为118%，超过安全阈值95%。计算过载功率：
$\Delta S_{L1} = (118\% - 95\%) \cdot 400MVA = 92MVA$

线路实际功率：$S_{L1} = 118\% \cdot 400MVA = 472MVA$

通过潮流计算，分解为有功和无功：
- 有功功率：$P_{L1} = 456MW$
- 无功功率：$Q_{L1} = 125Mvar$

功率因数：$\cos\phi = \frac{P_{L1}}{S_{L1}} = \frac{456}{472} = 0.966$

#### 候选调整集合确定

设定PTDF阈值$\varepsilon = 0.15$，形成候选调整集合：
$S_{L1} = \{s | |PTDF_{l,s}| > 0.15\} = \{A, B, C\}$

光伏电站D的PTDF值为0.12，低于阈值，不纳入候选调整集合。

#### 调整系数确定

考虑各发电站的技术特性、调整灵活性和经济性，设定调整系数：
- 风电场A：$\alpha_A = 1.05$（调整灵活性较高）
- 风电场B：$\alpha_B = 1.00$（基准值）
- 光伏电站C：$\alpha_C = 0.90$（调整成本较高）

#### 各发电站调整量计算

**风电场A调整量**：
$\Delta P_A = \alpha_A \cdot \frac{92}{0.72} \cdot \frac{320}{320+230+180} = 1.05 \cdot \frac{92}{0.72} \cdot \frac{320}{730} = 1.05 \cdot 127.78 \cdot 0.438 = 58.82MW$

**风电场B调整量**：
$\Delta P_B = \alpha_B \cdot \frac{92}{0.38} \cdot \frac{230}{320+230+180} = 1.00 \cdot \frac{92}{0.38} \cdot \frac{230}{730} = 1.00 \cdot 242.11 \cdot 0.315 = 76.26MW$

**光伏电站C调整量**：
$\Delta P_C = \alpha_C \cdot \frac{92}{0.25} \cdot \frac{180}{320+230+180} = 0.90 \cdot \frac{92}{0.25} \cdot \frac{180}{730} = 0.90 \cdot 368 \cdot 0.247 = 81.75MW$

#### 考虑技术约束的调整量优化

**风电场A**：
最小可调整单元为单台风机，单机容量2.5MW
最接近的可调整风机数量：$N_{cut,A} = \lceil \frac{58.82}{2.5} \rceil = 24$台
实际调整量：$\Delta P_A^{actual} = 24 \cdot 2.5 = 60MW$

**风电场B**：
最小可调整单元为单台风机，单机容量2.5MW
最接近的可调整风机数量：$N_{cut,B} = \lceil \frac{76.26}{2.5} \rceil = 31$台
实际调整量：$\Delta P_B^{actual} = 31 \cdot 2.5 = 77.5MW$

**光伏电站C**：
光伏可以连续调整逆变器输出功率
实际调整量：$\Delta P_C^{actual} = 81.75MW$

#### 调度指令执行方案

**风电场A执行方案**：
1. 根据风机位置和风速，选择24台位于风场外围且风速相对较低的风机
2. 发送远程控制指令，将这些风机切出运行
3. 实时监测风场总出力，确认降至目标值260MW（320MW-60MW）
4. 启动AGC自动功率控制，维持该出力水平

**风电场B执行方案**：
1. 按照预设的调度规则，选择31台风机进行切除
2. 分两批执行切除指令，每批间隔30秒，避免系统冲击
3. 新设定点为152.5MW（230MW-77.5MW）
4. 启动风场出力监控系统，确保调整效果

**光伏电站C执行方案**：
1. 通过中央控制系统向全部逆变器发送功率限制指令
2. 将逆变器总输出功率上限设为98.25MW（180MW-81.75MW）
3. 监测输出功率变化曲线，确保平稳过渡
4. 根据光照强度变化，动态调整限功率水平

#### 调度效果预测与验证

调度实施后预期的线路功率减少量：
$\Delta S_{L1} = 60MW \cdot 0.72 + 77.5MW \cdot 0.38 + 81.75MW \cdot 0.25 = 43.2 + 29.45 + 20.44 = 93.09MVA$

预期的新线路负载率：
$LR_{L1}^{new} = \frac{472MVA - 93.09MVA}{400MVA} \cdot 100\% = \frac{378.91}{400} \cdot 100\% = 94.73\%$

结果表明，执行调度方案后，线路L1的负载率将降至安全水平以下。

![image-20250808163958456](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250808163958456.png)

![image-20250808164020367](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250808164020367.png)

![image-20250808164032298](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250808164032298.png)

![image-20250808164044679](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250808164044679.png)
