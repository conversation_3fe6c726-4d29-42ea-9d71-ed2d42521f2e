# 电力系统脆弱性定义与构建方法

## 脆弱性的综合定义

### 核心定义

**脆弱性(Vulnerability)**是指电力系统承灾载体由于自身固有物理特性和对台风气象要素的响应特性共同作用而表现出的易损性程度。

$$V = f(P_{intrinsic}, M_{response})$$

其中：
- $V$：系统脆弱性
- $P_{intrinsic}$：系统固有物理属性
- $M_{response}$：对台风气象要素的响应属性（台风不发生时为0）

## 脆弱性构成要素

### 固有物理属性 ($P_{intrinsic}$)

#### 结构属性
**定义**：承灾载体的基本物理结构特征，不随外部灾害变化

**具体指标**：
- **设备年龄** ($A_{age}$)：设备投运年限，反映设备老化程度
  ```
  A_{age} = (当前年份 - 投运年份) / 设计寿命
  ```

- **设备材质强度** ($S_{material}$)：材料的抗拉强度、抗压强度等
  
  ```
  S_{material} = σ_{actual} / σ_{design}
  ```
  
- **设备载荷率** ($L_{load}$)：当前负荷与额定容量的比值
  ```
  L_{load} = P_{current} / P_{rated}
  ```

- **地理位置因子** ($G_{location}$)：地形、海拔、地质条件等
  ```
  G_{location} = w_1 × 地形系数 + w_2 × 海拔系数 + w_3 × 地质系数
  ```

#### 网络拓扑属性
- **节点度** ($D_{node}$)：节点连接的边数
- **介数中心性** ($B_{centrality}$)：节点在网络中的重要程度
- **聚类系数** ($C_{cluster}$)：节点邻居间的连接密度

### 台风气象要素响应属性 ($M_{response}$)

#### 风速响应属性 ($R_{wind}$)
**定义**：承灾载体对台风风速的响应程度

**风速暴露函数**：
$$E_{wind}(v) = \begin{cases}
0 & v < v_{threshold} \\
\frac{v - v_{threshold}}{v_{design} - v_{threshold}} & v_{threshold} \leq v \leq v_{design} \\
1 + \frac{v - v_{design}}{v_{ultimate} - v_{design}} & v_{design} < v \leq v_{ultimate} \\
2 & v > v_{ultimate}
\end{cases}$$

其中：
- $v$：台风风速 (m/s)
- $v_{threshold}$：设备开始受风速影响的阈值 (通常为8-12 m/s)
- $v_{design}$：设备设计抗风速度 (通常为25-35 m/s)
- $v_{ultimate}$：设备极限承受风速 (通常为45-60 m/s)

**风速敏感性系数**：
$$S_{wind} = \beta_1 \times 高度系数 + \beta_2 \times 迎风面积系数 + \beta_3 \times 结构刚度系数$$

**风速响应属性**：
$$R_{wind} = E_{wind}(v) \times S_{wind}$$

#### 降雨响应属性 ($R_{rain}$)
**定义**：承灾载体对台风降雨的响应程度

**降雨暴露函数**：
$$E_{rain}(r) = \begin{cases}
0 & r < r_{threshold} \\
\frac{r - r_{threshold}}{r_{heavy} - r_{threshold}} & r_{threshold} \leq r \leq r_{heavy} \\
1 + \frac{r - r_{heavy}}{r_{extreme} - r_{heavy}} & r_{heavy} < r \leq r_{extreme} \\
2 & r > r_{extreme}
\end{cases}$$

其中：
- $r$：台风降雨强度 (mm/h)
- $r_{threshold}$：设备开始受降雨影响的阈值 (通常为10-20 mm/h)
- $r_{heavy}$：大雨标准 (通常为50 mm/h)
- $r_{extreme}$：极端降雨标准 (通常为100 mm/h)

**降雨敏感性系数**：
$$S_{rain} = \gamma_1 \times 防水等级系数 + \gamma_2 \times 排水能力系数 + \gamma_3 \times 绝缘性能系数$$

**降雨响应属性**：
$$R_{rain} = E_{rain}(r) \times S_{rain}$$

#### 气压响应属性 ($R_{pressure}$)
**定义**：承灾载体对台风气压变化的响应程度

**气压暴露函数**：
$$E_{pressure}(p) = \begin{cases}
0 & p > p_{normal} \\
\frac{p_{normal} - p}{p_{normal} - p_{low}} & p_{low} \leq p < p_{normal} \\
1 + \frac{p_{low} - p}{p_{low} - p_{extreme}} & p_{extreme} \leq p < p_{low} \\
2 & p < p_{extreme}
\end{cases}$$

其中：
- $p$：台风中心气压 (hPa)
- $p_{normal}$：正常气压 (通常为1013 hPa)
- $p_{low}$：低气压阈值 (通常为980 hPa)
- $p_{extreme}$：极低气压阈值 (通常为920 hPa)

**气压敏感性系数**：
$$S_{pressure} = \delta_1 \times 密封性系数 + \delta_2 \times 结构强度系数$$

**气压响应属性**：
$$R_{pressure} = E_{pressure}(p) \times S_{pressure}$$

#### 台风要素耦合效应 ($C_{typhoon}$)
**定义**：台风多个气象要素同时作用时产生的协同效应

$$C_{typhoon} = \lambda \times E_{wind}(v) \times E_{rain}(r) \times E_{pressure}(p) \times 耦合强度系数$$

其中$\lambda > 0$表示台风各要素间存在正协同效应

## 脆弱性综合计算模型

### 基本模型
$$V = w_1 \times P_{intrinsic} + w_2 \times M_{response}$$

### 详细展开
$$V = w_1 \times (P_{structure} + P_{topology}) + w_2 \times (R_{wind} + R_{rain} + R_{pressure} + C_{typhoon})$$

### 具体计算公式
$$V = w_1 \times \left[\sum_{i} \alpha_i P_i\right] + w_2 \times \left[R_{wind} + R_{rain} + R_{pressure} + C_{typhoon}\right]$$

其中：
- $w_1, w_2$：固有属性和台风响应属性的权重系数
- $\alpha_i$：各固有属性的权重系数
- $P_i$：第i个固有物理属性

## 构建方法的理论依据

### 为什么这样构建？

#### 物理属性的必要性
**理论基础**：系统论和可靠性理论
- **设备老化理论**：设备性能随时间衰减，老化设备更易故障
- **材料力学理论**：材料强度决定承载能力上限
- **网络理论**：拓扑结构影响故障传播路径

**实际意义**：
- 反映系统内在的抗灾能力
- 提供脆弱性评估的基准线
- 独立于外部灾害的固有特征

#### 台风气象要素响应属性的必要性
**理论基础**：气象灾害理论和系统响应理论
- **暴露-响应理论**：系统对气象要素的响应程度取决于要素强度和系统特性
- **阈值理论**：气象要素只有超过特定阈值才对系统产生影响
- **多要素耦合理论**：台风的风、雨、压等要素存在协同作用

**实际意义**：
- 量化台风气象要素对系统的具体影响程度
- 体现系统对不同气象要素的差异化响应
- 支持基于气象预报的动态脆弱性评估

### 台风不发生时响应属性为0的合理性

#### 风速响应为0
**物理意义**：无台风时风速低于阈值，系统不受风载荷影响
**数学表达**：$R_{wind} = 0$ when $v < v_{threshold}$
**工程实际**：正常天气下风速通常为2-6 m/s，远低于设备受影响阈值

#### 降雨响应为0
**物理意义**：无台风时降雨强度低，不影响设备绝缘和运行
**数学表达**：$R_{rain} = 0$ when $r < r_{threshold}$
**工程实际**：正常降雨强度通常<10 mm/h，不会造成设备故障

#### 气压响应为0
**物理意义**：无台风时气压接近正常值，不产生压力差影响
**数学表达**：$R_{pressure} = 0$ when $p > p_{normal}$
**工程实际**：正常气压为1013±10 hPa，不影响设备密封性能

#### 耦合效应为0
**物理意义**：无台风时各气象要素均在正常范围，不存在协同作用
**数学表达**：$C_{typhoon} = 0$ when 任一气象要素暴露函数为0
