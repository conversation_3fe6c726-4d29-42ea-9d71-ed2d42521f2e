# 断面风险评估与应急措施

## 断面风险评估方法

### 断面定义与表示

断面是电力系统中将网络分割成两个或多个区域的一组输电线路集合。在数学上，我们可以用矩阵形式表示断面。
$$
\mathbf{S} = 
\begin{bmatrix}
s_{11} & s_{12} & \cdots & s_{1n} \\
s_{21} & s_{22} & \cdots & s_{2n} \\
\vdots & \vdots & \ddots & \vdots \\
s_{m1} & s_{m2} & \cdots & s_{mn}
\end{bmatrix}
$$

其中：
- $m$ 是系统中的断面数量
- $n$ 是系统中的线路总数
- $s_{ij} = 1$ 表示线路 $j$ 属于断面 $i$
- $s_{ij} = 0$ 表示线路 $j$ 不属于断面 $i$

### 断面传输能力

断面 $k$ 的传输能力定义为：

$$
P_{k,max} = \sum_{j=1}^{n} s_{kj} \cdot P_{j,max}
$$

其中：
- $P_{k,max}$ 是断面 $k$ 的最大安全传输能力
- $P_{j,max}$ 是线路 $j$ 的最大传输容量
- $s_{kj}$ 表示线路 $j$ 是否属于断面 $k$

### 断面实际传输功率

断面 $k$ 在时间 $t$ 的实际传输功率：

$$
P_{k,actual}(t) = \sum_{j=1}^{n} s_{kj} \cdot P_j(t)
$$

其中 $P_j(t)$ 是线路 $j$ 在时间 $t$ 的实际传输功率。

### 断面负载率

断面 $k$ 的负载率定义为：

$$
LR_k(t) = \frac{P_{k,actual}(t)}{P_{k,max}} \times 100\%
$$

### 断面风险评估指标

1. **负载率**：（==电力局确认==）
   $$
   Risk_{load,k}(t) = 
   \begin{cases}
   \text{低负载率}, & LR_k(t) \leq 70\% \\
   \text{中等风险}, & 70\% < LR_k(t) \leq 85\% \\
   \text{高风险}, & 85\% < LR_k(t) \leq 95\% \\
   \text{极高风险}, & LR_k(t) > 95\%
   \end{cases}
   $$

2. **线路状态**：（该断面内的线路的风险）
   $$
   Risk_{state,k}(t) = 1 - \prod_{j=1}^{n} (1 - s_{kj} \cdot O3_{prob,j}(t))
   $$
   
   其中，$O3_{prob,j}(t)$ 是线路 $j$ 在时间 $t$ 发生故障的概率。

3. **综合风险指标**：（==电力局确认==）
   $$
   Risk_{total,k}(t) = w_1 \cdot Risk_{load,k}(t) + w_2 \cdot Risk_{state,k}(t)
   $$
   
   其中，$w_1$ 和 $w_2$ 是权重系数，满足 $w_1 + w_2 = 1$。

### 断面安全阈值动态调整（==负荷差异怎么算==）（可变或者不可变）

考虑到夏季(大负荷)和冬季(小负荷)的差异，断面安全阈值可动态调整：

$$
P_{k,max}(t) = P_{k,max,base} \cdot f(L(t), T(t))
$$

其中：（==多几个参数==）
- $P_{k,max,base}$ 是基准最大传输能力
- $L(t)$ 是系统负荷水平
- $T(t)$ 是环境温度
- $f(L(t), T(t))$ 是调整函数
- $T_ref$是参照温度

例如，对于220kV线路，调整函数可表示为：（==确认调整函数，温度和负荷水平权重==
$$
f(L(t), T(t)) = 1 - 0.2 \cdot \frac{L(t)}{L_{max}} - 0.1 \cdot \max\left(\frac{T(t) - T_{ref}}{T_{max} - T_{ref}}, 0\right)
$$

## 断面风险判断与应急措施

### 风险判断标准

断面 $k$ 在时间 $t$ 的风险状态判断：（==确认==）

1. **预警状态**：当 $Risk_{total,k}(t) > Risk_{threshold,1}$ 或 $LR_k(t) > 80\%$
2. **告警状态**：当 $Risk_{total,k}(t) > Risk_{threshold,2}$ 或 $LR_k(t) > 90\%$
3. **紧急状态**：当 $Risk_{total,k}(t) > Risk_{threshold,3}$ 或 $LR_k(t) > 95\%$
4. **故障状态**：当断面中任一线路跳闸或 $LR_k(t) > 100\%$

### 情况一：原断面维持，调节未故障线路电流（==$ P_{k,required}$根据负荷曲线==）

当满足以下条件时，可采用调节措施维持原断面：

$$
\sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_{j,max} \geq P_{k,required}(t)
$$

其中：
- $\delta_j$ 表示线路 $j$ 是否故障（1表示故障，0表示正常）
- $P_{k,required}(t)$ 是断面 $k$ 在时间 $t$ 所需的最小传输能力

计算==属于断面且未故障==的线路的容量总和

当不等式成立时，意味着即使有线路故障，剩余未故障线路的总传输能力仍能满足系统需求，这表明可以通过调整剩余线路的负载分配来维持原有断面结构，无需重新划分网络。

当剩余容量足够时，优先通过调整未故障线路负载来维持系统稳定；只有在剩余容量不足时，才会启动更复杂的系统重构流程。



### 情况二：重新划分断面

当满足以下条件时，需要重新划分断面：

$$
\sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_{j,max} < P_{k,required}(t)
$$

或当调节措施无法在规定时间内完成时。

#### 新断面确定算法（==补充==）

1. **系统分区识别**：
   使用广度优先搜索(BFS)或深度优先搜索(DFS)算法，识别网络中断开后形成的孤岛。
   
   定义连接矩阵 $\mathbf{C}$，其中 $c_{ij} = 1$ 表示节点 $i$ 和节点 $j$ 之间有连接。

2. **关键节点识别**：
   使用图论中的割点算法识别系统中的关键节点。
   
   节点的重要性可用以下公式计算：（==电气距离==）
   $$I_i = \sum_{j=1}^{n} \frac{P_j}{d_{ij}}$$
   其中，$P_j$ 是节点 $j$ 的功率，$d_{ij}$ 是节点 $i$ 到节点 $j$ 的距离。

3. **新断面形成**：
   将网络划分为具有最小功率不平衡的子网络。
   
   子网络功率平衡度：
   $$Balance_k = \left|\frac{\sum_{i \in N_k} P_{gen,i} - \sum_{i \in N_k} P_{load,i}}{\sum_{i \in N_k} P_{load,i}}\right|$$
   其中，$N_k$ 是子网络 $k$ 中的节点集合。

## 调度模块

### 功率重分配算法

当断面中的某条线路发生故障（如跳闸）时，需要重新分配功率流以维持系统平衡。功率重分配算法如下：

#### 线路功率调整算法

当断面中的线路 $j_0$ 发生故障或过载时，其传输的功率 $P_{j_0}(t)$ 需要重新分配到剩余未故障线路。对于每条未故障线路 $j$，新的功率分配为：

$$
P_{j,new}(t) = P_j(t) + \Delta P_j(t)
$$

其中，功率增量 $\Delta P_j(t)$ 可以通过以下方式计算：

$$
\Delta P_j(t) = \frac{(P_{j,max} - P_j(t)) \cdot s_{kj} \cdot (1-\delta_j)}{\sum_{l=1}^{n} (P_{l,max} - P_l(t)) \cdot s_{kl} \cdot (1-\delta_l)} \cdot P_{j_0}(t)
$$

这种分配方式考虑了每条线路的剩余容量，确保功率分配更加合理。

$\Delta P_j(t)$ 表示分配给线路 $j$ 的额外功率，其中：

- $(P_{j,max} - P_j(t))$ 是线路 $j$ 的剩余容量

- $s_{kj}$ 表示线路 $j$ 是否属于断面 $k$（1表示属于，0表示不属于）

- $(1-\delta_j)$ 表示线路 $j$ 是否正常运行（1表示正常，0表示故障）

- $P_{j_0}(t)$ 是故障线路原本传输的功率，需要重新分配

分子：只有属于断面且未故障的线路才会参与分配，且分配比例与其剩余容量成正比。

分母：所有未故障线路剩余容量的总和，确保分配比例合理。

公式按照每条未故障线路的剩余容量比例，将故障线路的功率重新分配出去，考虑了线路是否属于当前断面以及是否处于正常运行状态。

### 备用电源接入策略

当原断面无法满足所需传输能力时，需要接入备用电源。

#### 备用电源选择

备用电源的选择基于以下优先级函数：

$$
Priority(i) = w_a \cdot \frac{P_{avail,i}}{P_{max,i}} + w_b \cdot \frac{1}{d_i} + w_c \cdot \frac{1}{C_i}
$$

其中：
- $P_{avail,i}$ 是备用电源 $i$ 的可用容量
- $P_{max,i}$ 是备用电源 $i$ 的最大容量
- $d_i$ 是备用电源 $i$ 到负荷中心的电气距离
- $C_i$ 是启动备用电源 $i$ 的成本
- $w_a$, $w_b$, $w_c$ 是权重系数，满足 $w_a + w_b + w_c = 1$

#### 备用电源接入流程

1. **容量缺口计算**：
   $$
   P_{deficit}(t) = P_{k,required}(t) - \sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_j(t)
   $$

2. **备用电源选择**：
   选择优先级最高的备用电源集合 $B$，使得：
   $$
   \sum_{i \in B} P_{avail,i} \geq P_{deficit}(t)
   $$

3. **新断面形成**：
   将选中的备用电源线路加入到断面矩阵中：（==备用电源都不在系统内==）
   $$
   s_{k,new,j} = 
   \begin{cases}
   s_{kj}, & \text{对于原有线路} \\
   1, & \text{对于新接入的备用电源线路} \\
   0, & \text{其他情况}
   \end{cases}
   $$

### 应急响应流程

#### 案例分析：线路跳闸场景

以一个四线路断面为例（如题干所述的L1、L2、L3、L4组成的断面）：

![断面示例图1](C:\Users\<USER>\Desktop\实习\源网荷储 - 副本\源网荷储 - 副本\建模\断面示例图1.jpg)

1. 初始状态：
   - 每条线路传输功率：$P_j(t) = 7 \text{ MW}$
   - 断面总传输功率：$P_{k,actual}(t) = 28 \text{ MW}$
   - 每条线路最大容量：$P_{j,max} = 10 \text{ MW}$

2. L1跳闸后：
   - 剩余线路最大容量总和：$3 \times 10 = 30 \text{ MW}$
   - 所需传输功率：$P_{k,required}(t) = 28 \text{ MW}$
   - 容量比率：$CapacityRatio = 30/28 > 1$

3. 功率重分配：
   - 每条剩余线路新功率：$P_{j,new}(t) = 28/3 \approx 9.33 \text{ MW}$
   - 每条线路的功率增量：$\Delta P_j(t) = 9.33 - 7 = 2.33 \text{ MW}$

   ![断面示例图2](C:\Users\<USER>\Desktop\实习\源网荷储 - 副本\源网荷储 - 副本\建模\断面示例图2.jpg)
   
4. 如果剩余线路容量不足（例如只能提供20 MW），则：
   - 容量缺口：$P_{deficit}(t) = 28 - 20 = 8 \text{ MW}$
   - 需要接入备用电源（如L5）提供8 MW功率
   - 形成新断面：L2、L3、L4、L5

通过以上调度模块，系统可以在线路故障情况下快速响应，维持电网稳定运行。
