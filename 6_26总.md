# 寒潮对广州市大型风机影响及电力系统连锁故障的数学建模

## 问题分析

极端天气事件如寒潮和雷击会对广州市的发电系统造成不同程度的影响。本模型旨在建立极端天气与发电性能降低之间的定量关系，并分析由此可能引发的电力系统爬坡事件、电气故障(E1-失压或过载)及对输电线路(O3-220KV线路)的连锁影响，为电力调度和电网安全运行提供风险预警依据。具体包括：

1. 寒潮与风机性能降低之间的关系，考虑地理位置（经纬度）、风向、风机属性等因素对这种影响的调节作用
2. 雷击与光伏电站性能降低之间的关系，考虑雷击强度、光伏电站布局、防护设施等因素
3. 分析风电和光伏功率共同波动对电力系统可能引发的连锁故障
4. 台风对风机和光伏系统的影响，考虑台风强度、距离、方向、设施敏感度等因素

---

## 模型假设

1. 寒潮强度可通过温度降幅、持续时间和风速量化，且三者影响可加权组合。  
2. 广州市各区域风机可通过经纬度坐标精确定位，光伏电站同样可通过地理坐标定位。
3. 寒潮影响随与寒潮中心的地理距离增大而衰减，风向影响影响程度。  
4. 风机对寒潮的敏感性与其安装高度、机型参数、使用年限等有关。  
5. 风机实际功率会随影响程度变化，并在高风险下进入停机保护状态。（对应4.1.7）  
6. 风机响应寒潮过程具有动态性，可用时间序列描述。
7. 雷击强度可通过雷电流幅值、持续时间和频次量化，对光伏电站的影响具有突发性。
8. 光伏电站对雷击的敏感性与其防雷设施、组件分布、连接方式等有关。
9. 光伏电站实际功率会因雷击降低，严重情况下会部分或完全离网。
10. 寒潮引起的集群风机功率变化和雷击引起的光伏电站功率变化若超过一定速率，将共同形成爬坡事件。
11. 电力系统存在关键断面和薄弱环节，受爬坡事件影响可能引发连锁故障。
12. 假设火力发电功率稳定
13. 台风对风机和光伏系统的影响是多种极端天气事件的综合效应，需要考虑台风强度、距离、方向、设施敏感度等因素

---

## 符号定义

| 符号                     | 含义                                              |
| ------------------------ | ------------------------------------------------- |
| $I_{ij}^W(t)$            | 寒潮 $j$ 在时间 $t$ 对区域 $i$ 风机的影响程度     |
| $I_{ik}^{PV}(t)$         | 雷击 $k$ 在时间 $t$ 对区域 $i$ 光伏电站的影响程度 |
| $S_i^W$                  | 区域 $i$ 风机的敏感度系数                         |
| $S_i^{PV}$               | 区域 $i$ 光伏电站的敏感度系数                     |
| $C_j^W(t)$               | 寒潮 $j$ 的时间变化强度                           |
| $C_k^{PV}(t)$            | 雷击 $k$ 的时间变化强度                           |
| $d_{ij}$                 | 区域 $i$ 与寒潮中心 $j$ 的距离                    |
| $d_{ik}$                 | 区域 $i$ 与雷击中心 $k$ 的距离                    |
| $\theta_{ij}$            | 风向与风机夹角                                    |
| $(x_i^W, y_i^W)$         | 区域 $i$ 风机的经纬度坐标                         |
| $(x_i^{PV}, y_i^{PV})$   | 区域 $i$ 光伏电站的经纬度坐标                     |
| $(x_j^c, y_j^c)$         | 寒潮 $j$ 中心的经纬度坐标                         |
| $(x_k^c, y_k^c)$         | 雷击 $k$ 中心的经纬度坐标                         |
| $T_j, W_j, D_j$          | 寒潮 $j$ 的温度降幅、风速、持续时间               |
| $L_k, F_k, A_k$          | 雷击 $k$ 的雷电流强度、频次、影响面积             |
| $H_i, A_i$               | 风机安装高度、使用年限                            |
| $V_{r_i}, T_{lim_i}$     | 风机额定风速、耐寒温度指标                        |
| $P_i^W, P_i^{W'}(t)$     | 正常/受影响下区域 $i$ 的风电发电功率              |
| $P_i^{PV}, P_i^{PV'}(t)$ | 正常/受影响下区域 $i$ 的光伏发电功率              |
| $R_i^W$                  | 风机区域调度风险评分                              |
| $R_i^{PV}$               | 光伏区域调度风险评分                              |
| $I_{\text{total}}^W$     | 广州市整体风电系统的损失程度                      |
| $I_{\text{total}}^{PV}$  | 广州市整体光伏系统的损失程度                      |
| $r(t_1, t_2)$            | 观测时段 $[t_1, t_2]$ 内的爬坡率                  |
| $r_{thres}$              | 系统可承受的爬坡率阈值                            |
| $P_{total}(t)$           | 时间 $t$ 的总风电和光伏出力                       |
| $V_{bus,k}(t)$           | 节点 $k$ 在时间 $t$ 的电压幅值                    |
| $V_{min,k}$              | 节点 $k$ 的最低允许电压                           |
| $S_{line,l}(t)$          | 线路 $l$ 在时间 $t$ 的视在功率                    |
| $S_{rated,l}$            | 线路 $l$ 的额定容量                               |
| $LR_l(t)$                | 线路 $l$ 在时间 $t$ 的负载率                      |
| $E1_{prob}(t)$           | 时间 $t$ 发生E1故障(失压或过载)的概率             |
| $O3_{prob}(t)$           | 时间 $t$ 影响O3-220KV线路的概率                   |
| $M_{il}^W$               | 风电场 $i$ 与线路 $l$ 的连接矩阵                  |
| $M_{il}^{PV}$            | 光伏电站 $i$ 与线路 $l$ 的连接矩阵                |
| $O3_{total}$             | O3-220KV线路系统的子线路集合                      |
| $O3_{critical}$          | O3线路系统中的关键线路集合                        |
| $P_{l,wind}(t)$          | 风电场对线路 $l$ 在时间 $t$ 的有功功率贡献        |
| $P_{l,pv}(t)$            | 光伏电站对线路 $l$ 在时间 $t$ 的有功功率贡献      |
| $Q_{l,wind}(t)$          | 风电场对线路 $l$ 在时间 $t$ 的无功功率贡献        |
| $Q_{l,pv}(t)$            | 光伏电站对线路 $l$ 在时间 $t$ 的无功功率贡献      |
| $P_{l,other}(t)$         | 其他电源对线路 $l$ 在时间 $t$ 的有功功率贡献      |
| $Q_{l,other}(t)$         | 其他电源对线路 $l$ 在时间 $t$ 的无功功率贡献      |
| $PTDF_{i,l}^{W,P}$       | 风电场 $i$ 对线路 $l$ 的有功功率传输分布因子      |
| $PTDF_{i,l}^{PV,P}$      | 光伏电站 $i$ 对线路 $l$ 的有功功率传输分布因子    |
| $PTDF_{i,l}^{W,Q}$       | 风电场 $i$ 对线路 $l$ 的无功功率传输分布因子      |
| $PTDF_{i,l}^{PV,Q}$      | 光伏电站 $i$ 对线路 $l$ 的无功功率传输分布因子    |
| $\phi_i^W$               | 风电场 $i$ 的功率因数角                           |
| $\phi_i^{PV}$            | 光伏电站 $i$ 的功率因数角                         |
| $Risk_{line,l}(t)$       | 线路 $l$ 在时间 $t$ 的风险等级                    |
| $Corr_{l}$               | 爬坡风险与线路风险的概率转移矩阵                  |
| $S_{O3,total}(t)$        | O3-220KV线路系统在时间 $t$ 的总视在功率           |
| $Ratio_l(t)$             | 线路 $l$ 在O3系统中的功率分配比例                 |
| $Section_{prob}(t)$      | 时间 $t$ 系统断面形成的概率                       |
| $Section_{threshold}$    | 系统断面风险警戒阈值                              |
| $LP_i$                   | 光伏电站 $i$ 的防雷保护等级                       |
| $DS_i$                   | 光伏电站 $i$ 的接线方式                           |
| $C_j^T(t)$               | 台风 $j$ 在时间 $t$ 的强度                        |
| $d_{ij}$                 | 设施 $i$ 与台风 $j$ 之间的距离                    |
| $f_T(d_{ij})$            | 台风距离衰减函数                                  |
| $g_T(\theta_{ij}^T)$     | 台风方向修正函数                                  |
| $S_i^{WT}$               | 风机 $i$ 对台风的敏感度                           |
| $PD_i^{WT}(t)$           | 风机 $i$ 在时间 $t$ 受到台风影响的物理损坏概率     |
| $P_i^{W'T}(t)$           | 台风条件下风机 $i$ 的实际输出功率                   |
| $S_i^{PVT}$              | 光伏系统 $i$ 对台风的敏感度                       |
| $LF_i(t)$                | 台风条件下光伏系统 $i$ 的光照因子                 |
| $I_{ij}^{PVT}(t)$        | 台风 $j$ 在时间 $t$ 对光伏系统 $i$ 的影响程度     |
| $PD_i^{PVT}(t)$          | 光伏系统 $i$ 在时间 $t$ 受到台风影响的物理损坏概率 |
| $P_i^{PV'T}(t)$          | 台风条件下光伏系统 $i$ 的实际输出功率               |

---

## 数学模型构建

### 第一阶段（A）：寒潮→O1.1-大型风电场功率波动

#### 寒潮强度模型

$$
C_j^W(t) = \alpha \cdot \frac{T_j(t) - \bar{T}}{\sigma_T} + \beta \cdot \log(1 + W_j(t)) + \gamma \cdot \sqrt{D_j(t)}
$$

**解释**：综合考虑寒潮的降温幅度（归一化处理）、风速（对风机结构影响大）、持续时间（影响风机疲劳累积）的影响，通过线性组合构建寒潮强度。（具身智能）

---

#### 距离计算模型

$$
d_{ij} = R \cdot \arccos\left( \sin y_i^W \cdot \sin y_j^c + \cos y_i^W \cdot \cos y_j^c \cdot \cos(x_i^W - x_j^c) \right)
$$

**解释**：使用球面余弦定理计算经纬度之间的地理距离，$R$ 为地球半径（约 6371 km），能较准确反映风机与寒潮中心之间的实际空间距离。

---

#### 距离衰减函数

$$
f(d_{ij}) =
\begin{cases}
1, & d_{ij} \leq d_0 \\\\
e^{-\lambda (d_{ij} - d_0)}, & d_{ij} > d_0
\end{cases}
$$

**解释**：假设在临近寒潮中心范围内影响最大，超过一定距离 $d_0$ 后呈指数衰减，$\lambda$ 表示衰减速率。==（$d_0$根据寒潮强度影响）==

寒潮分等级，不同等级确定不同的$d_0$

---

#### 风向修正函数

$$
g(\theta_{ij}) = \max(\cos(\theta_{ij}), 0)
$$

**解释**：风向越正对风机（$\theta_{ij}$ 越小），影响越强；当风是侧向或背向（$\theta_{ij}$ 接近 90° 或 180°）时影响减弱。

==🌀 1. **冷空气"冲击效应"更强**==

- 寒潮伴随着强冷空气团快速南下，这些空气温度极低、密度大、动量强。
- **当风向与风机正面对齐时（$\theta_{ij} \approx 0^\circ$）**，冷空气就像子弹一样直接撞向叶片和塔筒(g来计算)。

👉 冷却速度更快，金属材料更容易结霜、冻裂
 👉 部件受到的机械冲击更大（风剪力、叶片载荷）

---

#### 风机敏感度模型

$$
S_i^W = \delta_1 H_i + \delta_2 A_i + \delta_3 V_{r_i} + \delta_4 T_{lim_i} + \delta_5 F_i
$$

**解释**：敏感度由风机的物理和使用属性共同决定。高安装、高龄、低耐寒机型往往更容易受寒潮影响。

---

#### 影响程度模型

$$
I_{ij}^W(t) = S_i^W \cdot C_j^W(t) \cdot f(d_{ij}) \cdot \cos(\theta_{ij})
$$

**解释**：寒潮对风机的影响是寒潮强度、风机敏感度、地理距离、风向等多因素的乘积，体现协同作用。

$I_{ij}^W(t)$<1

---

#### 发电功率影响模型

$$
P_i^{W'}(t) =
\begin{cases}
P_i^W \cdot (1 - I_{ij}^W(t)), & I_{ij}^W(t) < I_{\text{critical}} \\\\
0, & I_{ij}^W(t) \geq I_{\text{critical}}
\end{cases}
$$

**解释**：若影响不大，风机功率将按比例下降；若影响超出临界值（如结冰风险过高），风机将主动停机保护。

---

#### 爬坡事件判定模型

$$
r(t_1, t_2) = \frac{|P_{total}(t_2) - P_{total}(t_1)|}{t_2 - t_1}
$$

当 $r(t_1, t_2) > r_{thres}$ 时，发生爬坡事件，其中：

$$
P_{total}(t) = \sum_{i=1}^{n} P_i^{W'}(t)
$$

**解释**：爬坡率表示单位时间内风电总输出的变化速率，当超过系统可承受阈值时，构成爬坡事件，可能威胁电网安全（风险）。

### 第一阶段（B）：雷击→光伏电站功率波动

#### 雷击强度模型

$$
C_k^{PV}(t) = \delta \cdot \frac{L_k(t)}{\bar{L}} + \epsilon \cdot \log(1 + F_k(t)) + \zeta \cdot \sqrt{A_k(t)}
$$

**解释**：雷击强度由雷电流强度（与平均值比较）、雷击频次（对持续冲击影响大）以及影响面积（影响光伏阵列规模）共同决定。

---

#### 光伏电站与雷击中心距离计算模型

$$
d_{ik} = R \cdot \arccos\left( \sin y_i^{PV} \cdot \sin y_k^c + \cos y_i^{PV} \cdot \cos y_k^c \cdot \cos(x_i^{PV} - x_k^c) \right)
$$

**解释**：使用球面余弦定理计算光伏电站与雷击中心之间的地理距离。

---

#### 雷击距离衰减函数

$$
f_{PV}(d_{ik}) =
\begin{cases}
1, & d_{ik} \leq d_0^{PV} \\\\
e^{-\lambda_{PV} (d_{ik} - d_0^{PV})}, & d_{ik} > d_0^{PV}
\end{cases}
$$

**解释**：在雷击中心一定范围内，影响最大；超过临界距离后影响呈指数衰减。

---

#### 光伏电站敏感度模型

$$
S_i^{PV} = \mu_1 \cdot LP_i + \mu_2 \cdot DS_i + \mu_3 \cdot A_i^{PV} + \mu_4 \cdot T_i^{PV}
$$

**解释**：光伏电站对雷击的敏感性由多种因素决定：

- $LP_i$：防雷保护等级，数值越高代表保护越好
- $DS_i$：电站接线方式，不同接线方式对雷击影响的抵抗能力不同
- $A_i^{PV}$：电站使用年限，年限越久可能防雷设施老化
- $T_i^{PV}$：电站技术类型（如单晶硅、多晶硅、薄膜等），不同类型组件抗雷击能力不同

---

#### 雷击影响程度模型

$$
I_{ik}^{PV}(t) = S_i^{PV} \cdot C_k^{PV}(t) \cdot f_{PV}(d_{ik})
$$

**解释**：雷击对光伏电站的影响是雷击强度、电站敏感度和地理距离的综合作用。

$I_{ik}^{PV}(t)$<1

---

#### 光伏发电功率影响模型

$$
P_i^{PV'}(t) =
\begin{cases}
P_i^{PV} \cdot (1 - I_{ik}^{PV}(t)), & I_{ik}^{PV}(t) < I_{\text{critical}}^{PV} \\\\
0, & I_{ik}^{PV}(t) \geq I_{\text{critical}}^{PV}
\end{cases}
$$

**解释**：若雷击影响不大，光伏电站功率将按比例下降；若影响超过临界值，光伏电站将进入保护状态或部分组件受损导致离网。

---

### 第一阶段（C）：台风→风机和光伏系统功率波动

#### 台风强度模型

$$
C_j^T(t) = \omega_1 \cdot \frac{V_j(t)}{V_{max}} + \omega_2 \cdot \frac{P_{std} - P_j(t)}{P_{std} - P_{min}} + \omega_3 \cdot \frac{R_j(t)}{R_{ref}} + \omega_4 \cdot \frac{PR_j(t)}{PR_{ref}}
$$

**解释**：台风强度模型综合考虑了台风的四个关键物理特性：最大风速、中心气压、七级风圈半径和降水强度。通过归一化处理和加权组合，形成统一的台风强度指数，用于量化台风对风机和光伏设施的潜在破坏能力。

- $\omega_1, \omega_2, \omega_3, \omega_4$：权重系数，满足$\sum_{i=1}^4 \omega_i = 1$
- $V_{max}$：参考最大风速，通常取75m/s（超强台风标准）
- $P_{std}$：标准大气压，1013.25百帕
- $P_{min}$：参考最低气压，通常取880百帕（超强台风标准）
- $R_{ref}$：参考七级风圈半径，通常取200公里
- $PR_{ref}$：参考降水强度，通常取100mm/h

---

#### 距离计算模型

$$
d_{ij} = R \cdot \arccos\left( \sin y_i^{fac} \cdot \sin y_j^T + \cos y_i^{fac} \cdot \cos y_j^T \cdot \cos(x_i^{fac} - x_j^T) \right)
$$

**解释**：使用球面余弦定理计算设施（风机或光伏电站）与台风中心之间的地理距离，其中$(x_i^{fac}, y_i^{fac})$代表设施的经纬度坐标，$(x_j^T, y_j^T)$代表台风中心的经纬度坐标，$R$为地球平均半径（约6371公里）。

---

#### 台风距离衰减函数

$$
f_T(d_{ij}) =
\begin{cases}
1, & d_{ij} \leq R_j(t) \\
e^{-\kappa_T \cdot (d_{ij} - R_j(t))}, & d_{ij} > R_j(t)
\end{cases}
$$

**解释**：在台风七级风圈范围内，影响保持最大；超出七级风圈后，影响强度呈指数衰减。$\kappa_T$为台风影响衰减系数，$R_j(t)$为台风七级风圈半径。此函数反映了台风影响的空间分布特征。

---

#### 台风方向修正函数

$$
g_T(\theta_{ij}^T) = 1 + \eta_T \cdot \cos(\theta_{ij}^T - \theta_j^T(t))
$$

**解释**：台风的影响会因设施相对于台风移动方向的位置而有所差异。位于台风移动前方的设施通常受到更强的影响（危险半圈效应），此函数通过考虑方位角差异来修正影响强度。$\eta_T$为方向影响因子，通常取0.2-0.5。

---

#### 风机台风敏感度模型

$$
S_i^{WT} = \mu_1 \cdot H_i + \mu_2 \cdot A_i + \mu_3 \cdot \frac{V_{cut,i}}{V_{ref}} + \mu_4 \cdot M_i + \mu_5 \cdot Y_i
$$

**解释**：风机对台风的敏感度由多个因素决定：安装高度、使用年限、切出风速、抗台风设计等级和偏航系统性能。安装高度越高、使用年限越长的风机通常对台风更敏感，而切出风速越高、抗台风设计等级越高的风机抵抗台风能力更强。

- $\mu_1, \mu_2, \mu_3, \mu_4, \mu_5$：权重系数
- $H_i$：风机安装高度
- $A_i$：风机使用年限
- $V_{cut,i}$：风机切出风速
- $V_{ref}$：参考切出风速（通常取25m/s）
- $M_i$：抗台风设计等级
- $Y_i$：偏航系统性能参数

---

#### 台风对风机影响程度模型

$$
I_{ij}^{WT}(t) = S_i^{WT} \cdot C_j^T(t) \cdot f_T(d_{ij}) \cdot g_T(\theta_{ij}^T)
$$

**解释**：台风对风机的影响程度是风机敏感度、台风强度、距离衰减和方向修正的综合结果。值域为[0,1]，0表示无影响，1表示最大影响。

---

#### 风机物理损坏概率模型

$$
PD_i^{WT}(t) = 1 - e^{-\gamma_W \cdot (I_{ij}^{WT}(t) - I_{thres}^{WT})}, \quad \text{if } I_{ij}^{WT}(t) > I_{thres}^{WT}
$$
$$
PD_i^{WT}(t) = 0, \quad \text{if } I_{ij}^{WT}(t) \leq I_{thres}^{WT}
$$

**解释**：此模型评估台风可能导致风机结构性损坏的概率。只有当影响程度超过特定阈值时，才考虑物理损坏的可能性；影响程度越高，损坏概率呈指数增长。$\gamma_W$为风机损坏率参数，$I_{thres}^{WT}$为物理损坏阈值。

---

#### 台风影响下风机功率模型

$$
P_i^{W'T}(t) =
\begin{cases}
0, & PD_i^{WT}(t) > PD_{thres}^{W} \text{ or } V_j(t) > V_{cut,i} \\
P_i^{W} \cdot (1 - I_{ij}^{WT}(t)), & I_{ij}^{WT}(t) < I_{critical}^{WT} \\
0, & I_{ij}^{WT}(t) \geq I_{critical}^{WT}
\end{cases}
$$

**解释**：此模型评估台风条件下风机的实际输出功率。风机会在三种情况下停机：物理损坏概率过高、台风影响程度超过临界值或风速超过切出风速。在安全运行范围内，功率输出按影响程度比例下降。

---

#### 光伏系统台风敏感度模型

$$
S_i^{PVT} = \tau_1 \cdot MS_i + \tau_2 \cdot FR_i + \tau_3 \cdot \frac{A_i^{PV}}{A_{ref}^{PV}}
$$

**解释**：光伏系统对台风的敏感度由其安装方式、固定牢固度和使用年限共同决定。不同安装方式（地面固定式、屋顶平铺式、水面漂浮式等）的光伏系统对台风的脆弱性不同，固定牢固度直接影响台风条件下支架结构的稳定性。

- $\tau_1, \tau_2, \tau_3$：权重系数
- $MS_i$：安装方式系数
- $FR_i$：固定牢固度系数
- $A_i^{PV}$：光伏系统使用年限
- $A_{ref}^{PV}$：参考使用年限（通常取25年）

---

#### 台风条件下光照因子模型

$$
LF_i(t) = 1 - \min\left(\nu_1 \cdot \frac{PR_j(t)}{PR_{max}} + \nu_2 \cdot C_j^T(t), 1\right)
$$

**解释**：台风期间，云层覆盖和强降水会显著降低到达光伏面板的光照强度。降水强度越大、台风强度越高，光照被阻挡越严重。$\nu_1, \nu_2$为权重系数，$PR_{max}$为参考最大降水强度（通常取150mm/h）。

---

#### 台风对光伏系统影响程度模型

$$
I_{ij}^{PVT}(t) = S_i^{PVT} \cdot C_j^T(t) \cdot f_T(d_{ij}) \cdot g_T(\theta_{ij}^T) \cdot (1 - LF_i(t))
$$

**解释**：台风对光伏系统的影响程度是系统敏感度、台风强度、距离因素、方向因素和光照因子的综合结果。光照因子影响功率输出，同时台风本身的物理影响反映在敏感度和强度中。

---

#### 光伏系统物理损坏概率模型

$$
PD_i^{PVT}(t) = 1 - e^{-\gamma_{PV} \cdot (I_{ij}^{PVT}(t) - I_{thres}^{PVT})}, \quad \text{if } I_{ij}^{PVT}(t) > I_{thres}^{PVT}
$$
$$
PD_i^{PVT}(t) = 0, \quad \text{if } I_{ij}^{PVT}(t) \leq I_{thres}^{PVT}
$$

**解释**：此模型评估台风可能导致光伏系统结构性损坏的概率。与风机类似，只有当影响程度超过特定阈值时，才考虑物理损坏的可能性。$\gamma_{PV}$为光伏系统损坏率参数，$I_{thres}^{PVT}$为物理损坏阈值。

---

#### 台风影响下光伏系统功率模型

$$
P_i^{PV'T}(t) =
\begin{cases}
0, & PD_i^{PVT}(t) > PD_{thres}^{PV} \\
P_i^{PV} \cdot LF_i(t) \cdot (1 - I_{ij}^{PVT}(t)), & I_{ij}^{PVT}(t) < I_{critical}^{PVT} \\
0, & I_{ij}^{PVT}(t) \geq I_{critical}^{PVT}
\end{cases}
$$

**解释**：此模型评估台风条件下光伏系统的实际输出功率。光伏系统会在两种情况下停机：物理损坏概率过高或影响程度超过临界值。在安全运行范围内，功率输出受光照因子和影响程度共同影响。

---

### 多灾害融合模型

极端天气条件下，风机和光伏系统往往会同时受到多种灾害的影响，例如寒潮、雷击和台风可能在同一时间段相继发生或同时发生。这种情况需要建立综合的多灾害融合模型，以评估复合灾害条件下发电系统的实际功率输出。

#### 风机多灾害影响融合模型（非独立伯努利事件的幂表达近似）

$$
P_i^{W'multi}(t) = P_i^W \cdot (1 - [1-(1-I_{ij}^{W}(t))^{\alpha_W} \cdot (1-I_{ij}^{WT}(t))^{\beta_W}]^{1/\gamma_W})
$$

**解释**：此模型考虑了风机同时受到寒潮和台风影响的情况。通过幂运算和乘积形式，模型捕捉了不同极端天气的协同作用效应，而非简单取最小值。当两种极端天气同时影响时，总影响大于任一单独影响，但小于简单相加。

- $I_{ij}^{W}(t)$：寒潮导致的风机影响程度
- $I_{ij}^{WT}(t)$：台风导致的风机影响程度
- $\alpha_W, \beta_W$：寒潮和台风影响的权重系数
- $\gamma_W$：协同作用因子，控制多灾害融合的非线性程度

---

#### 光伏系统多灾害影响融合模型

$$
P_i^{PV'multi}(t) = P_i^{PV} \cdot (1 - [1-(1-I_{ik}^{PV}(t))^{\alpha_{PV}} \cdot (1-I_{ij}^{PVT}(t))^{\beta_{PV}}]^{1/\gamma_{PV}})
$$

**解释**：此模型考虑了光伏系统同时受到雷击和台风影响的情况。雷击可能导致光伏组件损坏，而台风则可能通过强风和云层覆盖降低光伏发电效率。协同作用因子捕捉因一种灾害削弱系统后，另一种灾害造成更严重损害的现象。

- $I_{ik}^{PV}(t)$：雷击导致的光伏系统影响程度

- $I_{ij}^{PVT}(t)$：台风导致的光伏系统影响程度

- $\alpha_{PV}, \beta_{PV}$：雷击和台风影响的权重系数

- $\gamma_{PV}$：协同作用因子

  （1）分别计算“未受影响的比例”
  
  雷击未影响部分：
  $$
  1 - I_{ik}^{PV}(t)
  $$
  
  台风未影响部分：
  $$
  1 - I_{ij}^{PVT}(t)
  $$
  
  （2）加权幂次，反映灾害重要性
  
  雷击影响的权重：
  $$
  \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}}
  $$
  
  台风影响的权重：
  $$
  \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}}
  $$
  
  （3）乘积，反映协同未受影响部分
  
  两灾害共同作用下的剩余可用比例：
  $$
  \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}}
  $$
  
  （4）1减去乘积，得到“总损失比例”
  
  总损失比例（未考虑协同效应非线性）：
  $$
  1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right]
  $$
  
  （5）再用 $1/\gamma_{PV}$ 次方，调整协同效应强弱
  
  协同作用因子调整后：
  $$
  \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}}
  $$
  
  （6）最外层再用 1 减去，得到最终“未受影响比例”
  
  最终损失比例：
  $$
  1 - \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}}
  $$
  
  （7）乘以额定功率，得到实际输出功率
  
  多灾害影响下的实际输出功率：
  $$
  P_i^{PV'multi}(t) = P_i^{PV} \cdot \left[ 1 - \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}} \right]
  $$

---

#### 灾害协同效应动态调整模型

由于灾害间相互作用的复杂性，协同作用因子应当动态调整，以反映灾害强度变化对协同效应的影响：

$$
\gamma_{W}(t) = \gamma_{W,0} \cdot (1 + \delta_W \cdot I_{ij}^{WT}(t) \cdot I_{ij}^{W}(t))
$$

$$
\gamma_{PV}(t) = \gamma_{PV,0} \cdot (1 + \delta_{PV} \cdot I_{ij}^{PVT}(t) \cdot I_{ik}^{PV}(t))
$$

**解释**：协同系数随两种灾害的共同影响程度动态变化。当两种灾害同时达到高强度时，协同效应更为显著。

- $\gamma_{W,0}, \gamma_{PV,0}$：风机和光伏系统的基础协同系数
- $\delta_W, \delta_{PV}$：风机和光伏系统的协同增强因子
- $I_{ij}^{WT}(t) \cdot I_{ij}^{W}(t)$：台风和寒潮影响程度的乘积
- $I_{ij}^{PVT}(t) \cdot I_{ik}^{PV}(t)$：台风和雷击影响程度的乘积

---

#### 多灾害条件下系统爬坡率计算

$$
P_{total}^{multi}(t) = \sum_{i=1}^{n_W} P_i^{W'multi}(t) + \sum_{j=1}^{n_{PV}} P_j^{PV'multi}(t)
$$

$$
r^{multi}(t_1, t_2) = \frac{|P_{total}^{multi}(t_2) - P_{total}^{multi}(t_1)|}{t_2 - t_1}
$$

**解释**：多灾害条件下的爬坡率计算考虑了所有风机和光伏系统在综合影响下的实际输出功率。当爬坡率超过系统可承受阈值时，将触发电网安全风险。

## 状态空间定义（概率图）

**详细解释**：
状态空间是模型中所有可能状态的集合。为了应用马尔可夫模型，我们需要将现实世界中连续变化的物理量（如温度、风速、功率输出）转化为一组有限的、离散的状态。这使得我们能够用概率来描述系统从一个状态转移到另一个状态的过程。

马尔可夫链（Markov Chain）预测的核心思想是：**基于当前状态，利用状态转移概率来预测下一时刻或未来若干时刻的状态**。

### 灾害状态空间

1. 确定需要考虑的灾害类型（如寒潮、台风、雷击等）

2. 为每种灾害定义离散状态等级：
   $$
   S_t^{\text{hazard}} = [S_t^{h_1}, S_t^{h_2}, ..., S_t^{h_n}]
   $$
   **公式解释**：

   - $S_t^{\text{hazard}}$ 代表在时间点 `t` 的整体灾害状态向量。
   - 向量中的每个元素 $S_t^{h_i}$ 代表第 `i` 种灾害在时间点 `t` 的具体状态。
   - `n` 是所考虑的灾害类型的总数。

   - 例如，寒潮状态 $S_t^{\text{cold}}$ 可离散为：{0:无寒潮, 1:轻度寒潮, 2:中度寒潮, 3:重度寒潮}
   - 台风状态 $S_t^{\text{typhoon}}$ 可离散为：{0:无台风, 1:热带低压, 2:热带风暴, 3:台风, 4:强台风, 5:超强台风}

### 目标系统状态空间

1. 确定需要建模的系统组件（如风电、光伏、电网等）

2. 为每个组件定义离散状态等级：
   $$
   S_t^{\text{target}} = [S_t^{c_1}, S_t^{c_2}, ..., S_t^{c_m}]
   $$
   **公式解释**：

   - $S_t^{\text{target}}$ 代表在时间点 `t` 的目标系统的整体状态向量。
   - 向量中的每个元素 $S_t^{c_j}$ 代表第 `j` 个系统组件在时间点 `t` 的具体状态。
   - `m` 是所考虑的系统组件的总数。

   - 例如，风电状态 $S_t^{\text{wind}}$ 可离散为：{0:正常出力, 1:轻微降低, 2:显著降低, 3:严重降低}
   - 电网状态 $S_t^{\text{grid}}$ 可离散为：{0:稳定运行, 1:轻度压力, 2:中度压力, 3:高度压力, 4:局部故障}

### 完整状态空间

结合灾害状态和系统状态形成完整状态空间：
$$
S_t = [S_t^{\text{hazard}}, S_t^{\text{target}}]
$$
**公式解释**：

- $S_t$ 是在时间点 `t` 系统的完整状态，它是一个组合向量，同时包含了当时的灾害情况和系统的响应情况。这是构建耦合模型的基础，因为它将外部驱动因素（灾害）和内部响应（系统状态）联系在了一起。

## 数据收集与预处理

**详细解释**：
模型的准确性高度依赖于输入数据的质量和数量。这个阶段的目标是收集所需数据，并将其处理成模型可以使用的格式。

### 数据收集

1. 收集历史灾害数据：
   - 气象数据（温度、风速、风向、雷击等）
   - 灾害强度和持续时间记录
   - 空间分布信息

2. 收集系统响应数据：
   - 系统组件状态时间序列==（承载载体的属性状态）==
   - 故障记录和影响评估数据
   - 系统性能指标==（具体）==

### 数据预处理

1. 时间对齐：确保不同来源数据时间戳一致

2. 缺失值处理：使用插值或统计方法填补

3. 状态离散化：将连续变量转换为离散状态
   $$
   S_t^{\text{cold}} = \text{discretize}(\text{Temperature}_t, \text{thresholds}=[10°C, 5°C, 0°C])
   $$
   **公式解释**：

   - 这是一个示例，展示如何将连续的温度数据 `Temperature_t` 转换为离散的寒潮状态 $S_t^{\text{cold}}$。
   - `thresholds` 定义了状态划分的阈值。例如，温度高于10°C为状态0，5°C到10°C为状态1，0°C到5°C为状态2，低于0°C为状态3。

4. 数据分割：划分训练集和测试集（通常按时间顺序）

## 转移概率矩阵估计

**详细解释**：
这是马尔可夫模型的核心。我们通过分析历史数据来估计系统从一个状态转移到另一个状态的概率。这些概率被存储在一个称为"转移概率矩阵"的结构中。

### 一阶马尔可夫转移概率

**详细解释**：
一阶马尔可夫模型假设系统的下一个状态仅取决于当前状态，而与更早之前的状态无关。

1. 计算条件转移频次：
   $$
   N_{i,h,j} = \text{count}(S_t^{\text{target}}=i, S_t^{\text{hazard}}=h, S_{t+1}^{\text{target}}=j)
   $$
   **公式解释**：

   - $N_{i,h,j}$ 是一个计数，统计在历史数据中，当系统处于状态 `i` 且灾害状态为 `h` 时，在下一个时间步系统转移到状态 `j` 的次数。

2. 估计转移概率：(==有待商榷==)
   $$
   P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{\text{hazard}}=h) = \frac{N_{i,h,j}}{\sum_j\ N_{i,h,j}}
   $$
   **公式解释**：

   - 这个公式计算的是条件概率，即在当前系统状态为 `i`、灾害状态为 `h` 的条件下，系统下一个状态为 `j` 的概率。
   - 分子是转移到状态 `j` 的频次。
   - 分母是所有可能转移目标（所有）的频次之和，即从状态 `(i, h)` 出发的所有转移的总次数。

### 多阶马尔可夫转移概率

**详细解释**：
多阶（或k阶）马尔可夫模型放宽了一阶假设，认为系统的下一个状态取决于前面 `k` 个时刻的状态。这对于捕捉灾害的持续性影响（如寒潮持续多日）非常重要。

1. 确定马尔可夫链的阶数k（通过交叉验证或AIC/BIC准则）

2. 计算k阶条件转移频次：
   $$
   N_{i,h_t,...,h_{t-k+1},j} = \text{count}(S_t^{\text{target}}=i, S_t^{\text{hazard}}=h_t, ..., S_{t-k+1}^{\text{hazard}}=h_{t-k+1}, S_{t+1}^{\text{target}}=j)
   $$
   **公式解释**：

   - $N_{...}$ 同样是计数，但条件更为复杂。它统计的是在当前系统状态为 `i`，且当前及过去 `k-1` 个时间步的灾害状态序列为 `(h_t, h_{t-1}, ..., h_{t-k+1})` 的情况下，系统下一步转移到状态 `j` 的次数。

3. 估计k阶转移概率：
   $$
   P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{\text{hazard}}=h_t, ..., S_{t-k+1}^{\text{hazard}}=h_{t-k+1})
   = \frac{N_{i,h_t,...,h_{t-k+1},j}}{\sum_j N_{i,h_t,...,h_{t-k+1},j}}
   $$
   **公式解释**：

   - 该公式计算k阶转移概率。其逻辑与一阶类似，但条件部分包含了更多历史信息。

### 贝叶斯平滑处理

**详细解释**：
在真实数据中，某些状态转移可能从未发生过，导致其计数为0，从而计算出的概率也为0。这在预测中是有问题的。贝叶斯平滑通过给所有可能的转移"借"一点点概率，来避免出现绝对的零概率。

处理数据稀疏问题，避免零概率：
$$
P_{\text{smooth}}(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{\text{hazard}}=h) = \frac{N_{i,h,j} + \alpha \cdot \pi_j}{\sum_j(N_{i,h,j}) + \alpha}
$$
其中：

- $\alpha$是平滑参数，通常取小正数（如0.1-1）
- $\pi_j$是状态j的先验概率，可使用边缘分布估计
  **公式解释**：
- 分子中，真实频次 $N_{i,h,j}$ 加上了一个小的平滑项 $\alpha \cdot \pi_j$。
- 分母中，总频次也相应地加上了 $\alpha$（因为所有 $\pi_j$ 的和为1）。
- 即使 $N_{i,h,j}$ 为0，平滑后的概率也不为0，从而使模型更具鲁棒性。

## 混合马尔可夫模型构建

**详细解释**：
单一的马尔可夫模型可能无法捕捉复杂的系统行为。例如，系统在"有台风"和"无台风"两种模式下的行为动态可能完全不同。混合马尔可夫模型假设==存在多个（K个）==潜在的马尔可夫模型（称为"分量"）（K个一阶或多阶），而系统在任意时刻的行为是这些分量的加权混合。权重则由当前的灾害状态决定。

**通俗理解**：
想象一个人在不同天气下的行为模式：

- 晴天模式：喜欢户外活动，出门概率高
- 雨天模式：倾向于室内活动，出门概率低
- 暴雨模式：几乎不出门，在家概率极高

混合马尔可夫模型就是把这些不同的"行为模式"组合起来。当天气变化时，不同模式的"权重"也会变化。比如：

- 晴天时：晴天模式权重80%，雨天模式权重20%，暴雨模式权重0%
- 小雨时：晴天模式权重30%，雨天模式权重60%，暴雨模式权重10%
- 暴雨时：晴天模式权重5%，雨天模式权重25%，暴雨模式权重70%

### 模型结构定义

==**灾害状态决定各个马尔可夫分量的权重，然后将这些分量的转移概率矩阵按权重混合，得到系统在当前灾害条件下的整体转移概率。**==

这种设计的核心思想是：系统在不同灾害条件下可能表现出不同的行为模式，而混合马尔可夫模型通过动态调整各个"行为模式"的权重来捕捉这种变化。

1. 确定混合模型分量数K（通过交叉验证或BIC准则）

   这里确定混合模型分量数K是指确定需要多少个不同的马尔可夫模型来描述承载载体(即目标系统)的不同行为模式。

   在马尔可夫多阶耦合模型中，K表示的是混合马尔可夫模型中的分量数量，每个分量代表系统（整个模型）在不同条件下可能表现出的一种典型行为模式或运行状态。具体来说：

   1. 每个分量(k=1,2,...,K)是一个完整的马尔可夫链，有自己的状态转移概率矩阵 $P_k$
   2. 这些分量共同描述了承载载体(如电力系统)在不同灾害条件下的行为。

   **通俗解释**：就像确定需要几种"行为模式"来描述系统。

2. 定义混合马尔可夫模型：
   $$
   P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{\text{hazard}}=h) = \sum_{k=1}^K w_k(h) \cdot P_k(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i)
   $$
   其中：

   - $w_k(h)$是基于灾害状态h的混合权重
   - $P_k$是第k个分量马尔可夫链的转移概率
     **公式解释**：
   - 系统的整体转移概率是 `K` 个分量模型转移概率 $P_k$ 的加权和。
   - 权重 $w_k(h)$ 不是固定的，而是灾害状态 `h` 的函数。这意味着不同的灾害情况会激活不同的模型分量组合，从而实现对灾害影响的建模。

   **通俗理解**：

   - 就像一个智能音响，根据环境噪音自动调节音量
   - 当灾害轻微时，"正常运行模式"权重大
   - 当灾害严重时，"应急模式"权重大
   - 最终的系统行为是所有模式按权重混合的结果

3. 定义混合权重函数：(考虑时间、空间、强度耦合)
   $$
   w_k(h_1,h_2) = \frac{\exp(\alpha_k + \beta_k^1 h_1 + \beta_k^2 h_2 + \gamma_k h_1 h_2)}{\sum_{l=1}^K \exp(\alpha_l + \beta_l^1 h_1 + \beta_l^2 h_2 + \gamma_l h_1 h_2)}
   $$

## 详细参数解释

### 参数含义详解

#### 1. $\alpha_k$ - 基础权重参数（承载载体的默认概率）

**含义**：第k个分量模型的"基础活跃度"或"默认权重"
**通俗理解**：

- 就像每个人的性格基调，有些人天生乐观（$\alpha$大），有些人天生悲观（$\alpha$小）
- 在没有任何灾害发生时（$h_1=0, h_2=0$），这个参数决定了第k个分量被激活的基础概率
- 如果$\alpha_k$很大，说明第k个分量在"正常情况"下就比较活跃

**数学意义**：

- 当所有灾害状态都为0时，权重函数简化为：$w_k(0,0) = \frac{\exp(\alpha_k)}{\sum_{l=1}^K \exp(\alpha_l)}$
- $\alpha_k$越大，第k个分量在无灾害情况下的权重越大

**实际例子**：

- 假设有3个分量：正常运行模式($\alpha_1=2.0$)、轻度应急模式($\alpha_2=0.5$)、重度应急模式($\alpha_3=-1.0$)
- 在无灾害时，正常运行模式权重最大，重度应急模式权重最小

#### 2. $\beta_k^1, \beta_k^2$ - 主效应参数

**含义**：单个灾害对第k个分量权重的线性影响系数
**通俗理解**：

- $\beta_k^1$：灾害$h_1$对第k个分量的"敏感度"
- $\beta_k^2$：灾害$h_2$对第k个分量的"敏感度"
- 就像"下雨让人不想出门"，每种灾害都会独立地影响系统的行为模式

**数学意义**：

- $\beta_k^1 > 0$：灾害$h_1$越强，第k个分量被激活的概率越大
- $\beta_k^1 < 0$：灾害$h_1$越强，第k个分量被激活的概率越小
- $\beta_k^1 = 0$：灾害$h_1$对第k个分量没有影响

**实际例子**：

- 对于"正常运行模式"：$\beta_1^{\text{寒潮}} = -0.8$（寒潮越强，正常运行概率越小）
- 对于"应急模式"：$\beta_2^{\text{寒潮}} = 1.2$（寒潮越强，应急模式概率越大）

#### 3. $\gamma_k$ - 交互效应参数（耦合效应的核心）

**含义**：两种灾害同时发生时的额外影响系数
**通俗理解**：

- 这是**耦合效应建模的关键参数**！
- 描述"1+1是否等于2"的问题
- $\gamma_k > 0$：两种灾害一起发生时，影响会放大（协同效应，1+1>2）
- $\gamma_k < 0$：两种灾害一起发生时，影响会相互抵消（拮抗效应，1+1<2）
- $\gamma_k = 0$：两种灾害的影响完全独立（1+1=2）

**数学意义**：

- 交互项$\gamma_k h_1 h_2$只有当两种灾害都存在时才起作用
- 当$h_1=0$或$h_2=0$时，交互项为0，只有主效应起作用

**实际例子**：

- **正向耦合**（$\gamma_k > 0$）：寒潮+雷击同时发生
  - 寒潮使设备变脆，雷击更容易造成损坏
  - 单独寒潮：设备性能下降20%
  - 单独雷击：设备故障概率10%
  - 同时发生：设备故障概率可能达到40%（远超20%+10%）

- **负向耦合**（$\gamma_k < 0$）：某些情况下的相互抵消
  - 例如：强风+降雨，强风可能吹散雨云，减少降雨强度

### 公式的整体解释

其中：

- $\alpha_k, \beta_k^i, \gamma_k$是待估计参数
- $\gamma_k$捕捉灾害间的交互效应
  **公式解释**：
- 这是一个 `softmax` 函数（多分类逻辑回归），确保所有权重 $w_k$ 的和为1。
- $\alpha_k$ 是第k个分量的基础权重。
- $\beta_k^1, \beta_k^2$ 分别代表灾害 $h_1$ 和 $h_2$ 对激活第k个分量的线性影响（主效应）。
- $\gamma_k$ 是交互项系数，这是**耦合效应建模的关键**。如果 $\gamma_k$ 不为零，则表示两种灾害同时发生的影响不是它们各自影响的简单相加，存在着放大或抑制的耦合效应。

### 参数求解方法详解

#### 1. 数据准备阶段

**输入数据格式**：==（空间看情况加）==

```
时间t | 灾害1状态h1 | 灾害2状态h2 | 系统当前状态i | 系统下一状态j
------|------------|------------|--------------|-------------
  1   |     2      |     1      |      0       |      1
  2   |     2      |     2      |      1       |      2
  3   |     1      |     0      |      2       |      1
 ...  |    ...     |    ...     |     ...      |     ...
```

**数据说明**：

- 每一行代表一个状态转移观测
- $h_1, h_2$：两种灾害在时间t的强度等级（如0=无，1=轻度，2=中度，3=重度）
- $i$：系统在时间t的状态
- $j$：系统在时间t+1的状态

#### 2. EM算法求解过程

**算法概述**：
由于我们无法直接观测到系统当前处于哪个"潜在分量"==（潜在分类是可能出现的承载载体的状态？）==，模型参数的估计需要使用期望最大化（EM）算法。EM算法通过迭代的方式交替进行两步：E步（猜测每个数据点由哪个分量生成）和M步（基于这个猜测更新模型参数）。

**第一步：初始化参数**

```
随机初始化所有参数：
分量1: α₁ = 0.5,  β₁¹ = 0.2,  β₁² = 0.3,  γ₁ = 0.1
分量2: α₂ = -0.2, β₂¹ = 0.8,  β₂² = 0.4,  γ₂ = 0.3
分量3: α₃ = 0.1,  β₃¹ = -0.1, β₃² = 0.6,  γ₃ = -0.2
```

**第二步：E步骤（期望步骤）**
对每个数据点t，计算它属于每个分量（马尔科夫过程）k的概率（称为"责任"）：

$$r_{t,k} = \frac{w_k(h_{1t}, h_{2t}) \cdot P_k(j_t | i_t)}{\sum_{l=1}^K w_l(h_{1t}, h_{2t}) \cdot P_l(j_t | i_t)}$$

**具体计算示例**：
假设某个数据点：$h_1=2, h_2=1, i=0, j=1$

1. **计算每个分量的权重**：

   - $w_1(2,1) = \frac{\exp(0.5 + 0.2×2 + 0.3×1 + 0.1×2×1)}{\text{归一化因子}}$
     - 指数部分 = $0.5 + 0.4 + 0.3 + 0.2 = 1.4$
     - $w_1(2,1) = \frac{\exp(1.4)}{\text{归一化因子}}$

   - $w_2(2,1) = \frac{\exp(-0.2 + 0.8×2 + 0.4×1 + 0.3×2×1)}{\text{归一化因子}}$
     - 指数部分 = $-0.2 + 1.6 + 0.4 + 0.6 = 2.4$
     - $w_2(2,1) = \frac{\exp(2.4)}{\text{归一化因子}}$

   - $w_3(2,1) = \frac{\exp(0.1 + (-0.1)×2 + 0.6×1 + (-0.2)×2×1)}{\text{归一化因子}}$
     - 指数部分 = $0.1 - 0.2 + 0.6 - 0.4 = 0.1$
     - $w_3(2,1) = \frac{\exp(0.1)}{\text{归一化因子}}$

2. **结合转移概率计算责任**：
   假设各分量的点的转移概率为：$P_1(1|0)=0.3, P_2(1|0)=0.6, P_3(1|0)=0.8$

   $$r_{t,1} = \frac{w_1(2,1) × 0.3}{w_1(2,1) × 0.3 + w_2(2,1) × 0.6 + w_3(2,1) × 0.8}$$

**通俗理解E步**：

- 就像老师批改作业时，看到一份作业，要猜测这是哪个学生写的
- 每个学生（分量）都有自己的"写作风格"（权重函数）
- 根据作业的特点，计算每个学生写这份作业的可能性
- $r_{t,k}$就是"第t份作业是第k个学生写的概率"

**第三步：M步骤（最大化步骤）**
使用计算出的责任更新参数：

1. **更新转移概率矩阵**：
   $$P_k(j|i) = \frac{\sum_t r_{t,k} × I(i_t=i, j_t=j)}{\sum_t r_{t,k} × I(i_t=i)}$$

   **通俗理解**：

   - 就像根据学生的"作业风格"来更新对每个学生能力的评估
   - 如果某个学生写某类题目的概率高，就增加他在这类题目上的"擅长度"
   - $I(...)$是指示函数，满足条件时为1，否则为0

2. **更新权重参数**（使用梯度下降）：

   **梯度计算**：

   - 对于$\alpha_k$的梯度：
     $$\frac{\partial L}{\partial \alpha_k} = \sum_t (r_{t,k} - w_k(h_{1t}, h_{2t}))$$

   - 对于$\beta_k^1$的梯度：
     $$\frac{\partial L}{\partial \beta_k^1} = \sum_t (r_{t,k} - w_k(h_{1t}, h_{2t})) × h_{1t}$$

   - 对于$\beta_k^2$的梯度：
     $$\frac{\partial L}{\partial \beta_k^2} = \sum_t (r_{t,k} - w_k(h_{1t}, h_{2t})) × h_{2t}$$

   - 对于$\gamma_k$的梯度：
     $$\frac{\partial L}{\partial \gamma_k} = \sum_t (r_{t,k} - w_k(h_{1t}, h_{2t})) × h_{1t} × h_{2t}$$

   **参数更新公式**：
   $$\alpha_k^{\text{new}} = \alpha_k^{\text{old}} + \eta × \frac{\partial L}{\partial \alpha_k}$$
   $$\beta_k^{1,\text{new}} = \beta_k^{1,\text{old}} + \eta × \frac{\partial L}{\partial \beta_k^1}$$
   $$\beta_k^{2,\text{new}} = \beta_k^{2,\text{old}} + \eta × \frac{\partial L}{\partial \beta_k^2}$$
   $$\gamma_k^{\text{new}} = \gamma_k^{\text{old}} + \eta × \frac{\partial L}{\partial \gamma_k}$$

   其中$\eta$是学习率（如0.01）

   **通俗理解梯度**：

   - 梯度就是"改进方向"
   - 如果$r_{t,k} > w_k$，说明第k个分量被"低估"了，需要增加相应参数
   - 如果$r_{t,k} < w_k$，说明第k个分量被"高估"了，需要减少相应参数
   - $\gamma_k$的梯度包含$h_{1t} × h_{2t}$项，只有当两种灾害同时存在时才有贡献

**第四步：收敛判断**
计算对数似然函数：
$$L = \sum_t \log\left(\sum_k w_k(h_{1t}, h_{2t}) × P_k(j_t | i_t)\right)$$

**收敛条件**：

- 如果$|L_{\text{new}} - L_{\text{old}}| < \epsilon$（如$\epsilon = 10^{-6}$），则算法收敛
- 或者达到最大迭代次数（如1000次）

#### 3. 参数解释的实际意义

**训练完成后的参数示例**：

```
分量1（正常运行模式）：
α₁ = 1.2   # 基础权重高，正常情况下主导
β₁¹ = -0.8 # 寒潮负影响，寒潮越强此模式越不活跃
β₁² = -0.5 # 雷击负影响
γ₁ = -0.2  # 负耦合，两种灾害同时发生时相互抵消部分影响

分量2（轻度应急模式）：
α₂ = 0.3   # 中等基础权重
β₂¹ = 0.6  # 寒潮正影响，寒潮激活此模式
β₂² = 0.4  # 雷击正影响
γ₂ = 0.1   # 轻微正耦合

分量3（重度应急模式）：
α₃ = -1.5  # 基础权重很低，正常情况下几乎不激活
β₃¹ = 1.2  # 寒潮强正影响
β₃² = 1.0  # 雷击强正影响
γ₃ = 0.8   # 强正耦合，两种灾害同时发生时大幅激活此模式
```

**参数的物理意义**：

- $\gamma_3 = 0.8 > 0$说明在重度应急模式下，寒潮和雷击存在强烈的协同破坏效应
- 当$h_1=3, h_2=2$时，交互项贡献为$0.8 × 3 × 2 = 4.8$，这是一个很大的数值
- 这意味着两种高强度灾害同时发生时，系统进入重度应急模式的概率会急剧上升

**权重计算示例**：
在灾害状态$(h_1=3, h_2=2)$下：

- 分量1权重：$w_1(3,2) = \frac{\exp(1.2 - 0.8×3 - 0.5×2 - 0.2×3×2)}{\text{归一化}}$
  - 指数部分 = $1.2 - 2.4 - 1.0 - 1.2 = -3.4$
  - 权重很小，正常模式几乎不激活

- 分量3权重：$w_3(3,2) = \frac{\exp(-1.5 + 1.2×3 + 1.0×2 + 0.8×3×2)}{\text{归一化}}$
  - 指数部分 = $-1.5 + 3.6 + 2.0 + 4.8 = 8.9$
  - 权重很大，重度应急模式被强烈激活

**通俗理解**：

- 就像人在面对多重压力时的反应
- 单独的工作压力或家庭压力还能应付
- 但两种压力同时来临时，崩溃的概率会急剧上升
- $\gamma$参数就是量化这种"压垮骆驼的最后一根稻草"效应

#### 4. 完整求解流程总结

**输入**：历史数据 $\{(h_{1t}, h_{2t}, i_t, j_t)\}_{t=1}^T$
**输出**：训练好的参数 $\{\alpha_k, \beta_k^1, \beta_k^2, \gamma_k, P_k\}_{k=1}^K$

**算法步骤**：

1. **初始化**：随机设置所有参数
2. **迭代优化**：
   - E步：计算每个数据点的分量归属概率$r_{t,k}$
   - M步：基于$r_{t,k}$更新所有参数
   - 检查收敛：计算对数似然函数变化
3. **输出结果**：收敛后的参数即为最终模型

**计算复杂度**：

- 时间复杂度：$O(T \times K \times \text{迭代次数})$
- 空间复杂度：$O(K \times |S|^2)$，其中$|S|$是状态空间大小

## 耦合效应量化

**详细解释**：
模型训练完成后，我们可以从参数中提取有价值的信息，来定量地描述灾害间的耦合效应。

### 强度耦合系数计算

#### 基本公式

计算灾害间的耦合系数：
$$
\lambda_{h_1,h_2} = \sum_k \gamma_k \cdot w_k(h_1,h_2)
$$

#### 详细解释

**公式含义**：

- $\gamma_k$ 是第k个分量的交互项系数
- $w_k(h_1,h_2)$ 是在特定灾害组合 $(h_1, h_2)$ 下，第k个分量被激活的权重
- $\lambda_{h_1,h_2}$ 是在特定灾害组合下，整体的"净"耦合效应强度，它是所有分量耦合效应的加权平均

**判断标准**：

- 当$\lambda_{h_1,h_2} > 0$时，表示**正向放大效应**（协同破坏）
- 当$\lambda_{h_1,h_2} < 0$时，表示**抑制效应**（相互抵消）
- 当$\lambda_{h_1,h_2} = 0$时，表示**独立效应**（无耦合）

#### 计算示例

假设在灾害状态$(h_1=2, h_2=1)$下：

```
分量权重：w₁(2,1) = 0.2, w₂(2,1) = 0.5, w₃(2,1) = 0.3
交互系数：γ₁ = -0.2, γ₂ = 0.1, γ₃ = 0.8

耦合系数计算：
λ(2,1) = (-0.2)×0.2 + 0.1×0.5 + 0.8×0.3
       = -0.04 + 0.05 + 0.24
       = 0.25 > 0
```

**结果解释**：$\lambda(2,1) = 0.25 > 0$，说明中度寒潮+轻度雷击组合存在正向耦合效应，两种灾害同时发生的破坏力超过各自单独影响的简单相加。

#### 不同强度组合的耦合效应分析

```
灾害组合分析表：
h₁\h₂  |  0   |  1   |  2   |  3
-------|------|------|------|------
   0   | 0.00 | 0.00 | 0.00 | 0.00  # 无耦合（单一灾害）
   1   | 0.00 | 0.15 | 0.28 | 0.45  # 轻度耦合
   2   | 0.00 | 0.25 | 0.52 | 0.85  # 中度耦合
   3   | 0.00 | 0.45 | 0.85 | 1.35  # 强度耦合
```

**观察规律**：

- 对角线（相同强度）：耦合效应最强
- 灾害强度越高，耦合效应越明显
- 存在"阈值效应"：只有达到一定强度才出现显著耦合

### 时序耦合分析（适用于马尔科夫阶数>1）

#### 基本概念

**时序耦合**：研究灾害发生的先后顺序对系统影响的差异

#### 分析公式

比较不同灾害发生顺序的影响差异：
$$
\Delta P = P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{h_1}=\text{high}, S_{t-1}^{h_2}=\text{medium}) - P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}=i, S_t^{h_2}=\text{medium}, S_{t-1}^{h_1}=\text{high})
$$

#### 详细解释

**公式含义**：

- 此公式用于分析灾害发生的**时序**是否重要
- 它比较了两种情况下系统转移到状态 `j` 的概率差异：
  1. **情况A**：先发生中度灾害 $h_2$，再发生高度灾害 $h_1$
  2. **情况B**：先发生高度灾害 $h_1$，再发生中度灾害 $h_2$
- 如果 $\Delta P$ 显著不为零，则说明灾害发生的顺序会影响系统响应

**通俗理解**：

- 就像人生病的顺序很重要
- 先感冒再发烧 vs 先发烧再感冒，对身体的影响可能不同
- 有些情况下，"雪上加霜"比"雨后天晴"更严重

#### 实际应用示例

**案例：寒潮-雷击时序效应**

```
场景设定：
- 系统当前状态：i = 1（轻微受损）
- 目标状态：j = 3（严重受损）
- 灾害强度：寒潮=3级，雷击=2级

情况A：先雷击后寒潮
t-1: 雷击2级  →  t: 寒潮3级  →  t+1: 系统状态？
P(A) = P(j=3 | i=1, 寒潮=3, 雷击(t-1)=2) = 0.65

情况B：先寒潮后雷击
t-1: 寒潮3级  →  t: 雷击2级  →  t+1: 系统状态？
P(B) = P(j=3 | i=1, 雷击=2, 寒潮(t-1)=3) = 0.45

时序效应：ΔP = 0.65 - 0.45 = 0.20 > 0
```

**结果解释**：

- $\Delta P = 0.20 > 0$：先雷击后寒潮的破坏更严重
- **物理机制**：雷击先造成设备微损伤，寒潮使材料变脆，加剧损伤扩展
- **反向顺序**：寒潮虽然使材料变脆，但雷击时设备已经"适应"了低温环境

#### 时序效应的类型

**1. 累积效应**（$\Delta P > 0$）

- 后发生的灾害在前一个灾害基础上造成更大破坏
- 例子：地震→余震，台风→暴雨

**2. 适应效应**（$\Delta P < 0$）

- 系统对第一个灾害产生"适应性"，减轻后续灾害影响
- 例子：轻微故障→系统自动保护→减少后续损失

**3. 无序效应**（$\Delta P ≈ 0$）

- 灾害顺序对系统影响无显著差异
- 例子：两种独立的环境因素

#### 时序窗口分析

研究不同时间间隔下的耦合效应：

```
时间间隔    |  耦合强度  |  物理解释
-----------|-----------|------------------
Δt = 1小时  |   0.85    | 直接叠加效应
Δt = 6小时  |   0.62    | 部分恢复
Δt = 24小时 |   0.28    | 系统自愈
Δt = 72小时 |   0.05    | 基本独立
```

**观察规律**：

- 时间间隔越短，耦合效应越强
- 存在"记忆衰减"：系统会逐渐"忘记"之前的灾害影响
- 临界时间窗口：通常在24-48小时内

### 空间耦合分析

#### 基本概念

**空间耦合**：研究不同地理位置之间灾害的相互关联性和传播效应

#### 分析公式

计算空间协方差矩阵：
$$
\Sigma_{ij}(x,y) = \text{Cov}(S_t^{h_i}(x), S_t^{h_j}(y))
$$
其中，$S_t^{h_i}(x)$表示第i种灾害在位置x的状态

#### 详细解释

**公式含义**：

- 此公式用于分析灾害在**空间**上的关联性
- $\text{Cov}(...)$ 计算的是协方差，衡量两个变量的线性相关程度
- $\Sigma_{ij}(x,y)$ 衡量了在不同地理位置 `x` 和 `y` 发生的灾害 `i` 和 `j` 之间的统计相关性

**通俗理解**：

- 就像研究"一个地方下雨，另一个地方是否也容易下雨"
- 或者"A市发生地震，B市是否也容易发生地震"
- 空间耦合反映了灾害的"传染性"或"连锁反应"

#### 空间相关性类型

**1. 正相关**（$\Sigma_{ij} > 0$）

- 一个地方发生灾害，另一个地方也容易发生相同或相关灾害
- 例子：台风路径上的连续城市都会受影响

**2. 负相关**（$\Sigma_{ij} < 0$）

- 一个地方发生灾害，另一个地方发生该灾害的概率降低
- 例子：上游泄洪，下游洪水风险增加，但上游洪水风险降低

**3. 无相关**（$\Sigma_{ij} ≈ 0$）

- 两个地方的灾害发生相互独立
- 例子：相距很远的两个城市的地震

#### 实际应用示例

**案例：电网系统空间耦合分析**

```
地理布局：
A市 ←--50km--→ B市 ←--80km--→ C市
 |                |               |
电厂            变电站          负荷中心

空间协方差矩阵（寒潮灾害）：
      A市    B市    C市
A市 [ 1.00  0.75  0.45 ]
B市 [ 0.75  1.00  0.82 ]
C市 [ 0.45  0.82  1.00 ]
```

**结果解释**：

- A-B相关性(0.75)：距离较近，天气系统相似
- B-C相关性(0.82)：更高相关性，可能存在地形或气候因素
- A-C相关性(0.45)：距离最远，相关性最低

#### 空间传播模型

**距离衰减模型**：
$$
\Sigma_{ij}(d) = \Sigma_{ij}(0) \cdot \exp(-\alpha \cdot d)
$$
其中：

- $d$ 是两地之间的距离
- $\alpha$ 是衰减系数
- $\Sigma_{ij}(0)$ 是同一位置的相关性（通常为1）

**实际参数示例**：

```
灾害类型     | 衰减系数α | 有效影响半径
------------|----------|-------------
寒潮        |   0.02   |   ~200km
雷击        |   0.15   |   ~30km
台风        |   0.01   |   ~500km
地震        |   0.25   |   ~20km
```

#### 空间耦合的工程意义

**1. 风险传播预警**

- 当A地发生灾害时，可以预测B地的风险概率
- 提前采取防护措施

**2. 资源配置优化**

- 根据空间相关性合理配置应急资源
- 避免资源在相关性高的区域重复配置

**3. 系统脆弱性评估**

- 识别"关键节点"：影响范围大的地理位置
- 评估"连锁故障"风险

#### 多维空间分析

**三维空间耦合**：
$$
\Sigma_{ijk}(x,y,z) = \text{Cov}(S_t^{h_i}(x), S_t^{h_j}(y), S_t^{h_k}(z))
$$

**应用场景**：

- 山区：考虑海拔高度影响
- 海岸：考虑距海距离影响
- 城市：考虑建筑密度影响

## 模型验证与调优

**详细解释**：
建立模型后，必须系统地评估其性能，并对关键参数进行调整以达到最优效果。

### 交叉验证

1. 使用时间序列交叉验证方法评估模型性能
2. 计算评估指标：
   - 对数似然（Log-likelihood）：衡量模型对观测数据的解释程度，值越大越好。
   - 困惑度（Perplexity）：对数似然的指数形式，值越小越好。
   - 预测准确率（Accuracy）：模型正确预测下一个状态的比例。
   - AIC/BIC信息准则：在模型拟合度和复杂性之间进行权衡的指标，用于模型选择（如确定阶数k或分量数K），值越小越好。

### 模型调优

1. 调整马尔可夫链阶数k
2. 调整混合模型分量数K
3. 调整贝叶斯平滑参数$\alpha$
4. 优化状态空间离散化阈值

### 敏感性分析

1. 分析模型对参数变化的敏感性
2. 识别关键参数和重要灾害类型
3. 评估不同数据量下的模型性能

## 预测与应用

### 状态预测

1. 一步预测：
   $$
   P(S_{t+1}^{\text{target}}=j | S_t^{\text{target}}, S_t^{\text{hazard}}, ..., S_{t-k+1}^{\text{hazard}})
   $$
   **公式解释**：

   - 直接使用已训练好的模型，输入当前（及历史）状态，计算出系统在下一个时间步转移到每个可能状态 `j` 的概率分布。

2. 多步预测：
   $$
   P(S_{t+n}^{\text{target}}=j | S_t^{\text{target}}, S_t^{\text{hazard}}, ..., S_{t-k+1}^{\text{hazard}}) = \sum_{\text{paths}} \prod_{l=0}^{n-1} P(S_{t+l+1}^{\text{target}} | S_{t+l}^{\text{target}}, S_{t+l}^{\text{hazard}}, ...)
   $$
   **公式解释**：

   - 预测未来 `n` 步之后的状态。这需要考虑从当前到未来 `n` 步之间所有可能的演化路径。
   - $\sum_{\text{paths}}$ 表示对所有可能的中间路径进行求和。
   - $\prod$ 表示将路径上每一步的转移概率连乘起来。
   - 这通常通过矩阵乘法或蒙特卡洛模拟来实现。

## 实现细节

### 数据结构

1. 状态转移数据存储格式：
   $$
   \{\text{时间戳}, S_t^{\text{hazard}}, S_t^{\text{target}}, S_{t+1}^{\text{target}}\}
   $$

2. 转移概率矩阵存储格式：

   - 对于一阶马尔可夫模型：三维张量 [i, h, j]，维度分别为（当前系统状态数，灾害状态数，下一系统状态数）。
   - 对于k阶马尔可夫模型：(k+2)维张量，维度更复杂。

## 具体算法步骤

### 模型训练完整流程

```
输入：历史灾害数据和系统响应数据
输出：马尔可夫多阶耦合模型参数

1. 数据预处理
   1.1 时间对齐
   1.2 缺失值处理
   1.3 状态离散化

2. 状态空间定义
   2.1 定义灾害状态空间
   2.2 定义系统状态空间

3. 确定模型超参数（Hyperparameters）
   3.1 确定马尔可夫链阶数k（通过AIC/BIC或交叉验证）
   3.2 确定混合模型分量数K（通过AIC/BIC或交叉验证）
   3.3 设置贝叶斯平滑参数α

4. 转移概率矩阵估计
   4.1 计算条件转移频次
   4.2 应用贝叶斯平滑
   4.3 计算转移概率矩阵

5. 混合马尔可夫模型训练（如果使用）
   5.1 初始化模型参数
   5.2 执行EM算法迭代
      5.2.1 E步骤：计算后验概率（责任）
      5.2.2 M步骤：更新模型参数
   5.3 检查收敛性

6. 模型验证
   6.1 在测试集上评估模型性能
   6.2 计算评估指标（如准确率、对数似然）
   6.3 必要时返回步骤3，调整模型超参数

7. 耦合效应量化
   7.1 计算强度耦合系数
   7.2 分析时序耦合效应
   7.3 分析空间耦合效应

8. 返回训练好的模型参数
```

### 模型预测流程

```
输入：当前系统状态、当前及历史灾害状态、训练好的模型参数
输出：系统未来状态预测

1. 状态准备
   1.1 确保输入状态符合模型定义
   1.2 处理缺失状态（如有）

2. 一步预测
   2.1 计算混合权重（基于当前灾害状态）
   2.2 计算各分量转移概率
   2.3 计算加权和得到总体转移概率分布
   2.4 返回概率最高的状态或完整的概率分布

3. 多步预测（如需）
   3.1 初始化路径集合
   3.2 对每个时间步执行一步预测，并假定未来的灾害情景
   3.3 更新路径概率
   3.4 返回最终时间步的状态分布
```

## 参数估计详细算法

### 最大似然估计

**输入**：状态转移序列 $D = \{(S_t^{\text{target}}, S_t^{\text{hazard}}, S_{t+1}^{\text{target}})\}$
**输出**：转移概率矩阵 $P$

1. 初始化计数矩阵 $N[i,h,j] = 0$。

   -   *注：i, h, j 分别是当前系统状态、灾害状态、下一系统状态。*

2. 遍历数据集中的每个转移 $(S_t^{\text{target}}=i, S_t^{\text{hazard}}=h, S_{t+1}^{\text{target}}=j)$：

   -   $N[i,h,j] \mathrel{+}= 1$  // 对应计数加一

3. 遍历每个状态对 (i,h)：

   - 计算总计数 $\text{total}$：
     $$
     \text{total} = \sum_j N[i,h,j]
     $$

   - 如果 $\text{total} > 0$：

     - 对于每个目标状态 j, 计算概率 $P[i,h,j]$:
       $$
       P[i,h,j] = \frac{N[i,h,j]}{\text{total}}
       $$

   - 否则 (如果(i,h)从未在数据中出现过):

     - 使用均匀分布赋予一个无信息的先验:
       $$
       P[i,h,j] = \frac{1}{|J|}
       $$
       其中 $|J|$ 是目标状态空间的大小。

4. 返回 $P$。

### 贝叶斯估计（带平滑）

**输入**：状态转移序列 $D$、平滑参数 $\alpha$、先验概率 $\pi$
**输出**：平滑后的转移概率矩阵 $P_{\text{smooth}}$

1. 初始化计数矩阵 $N[i,h,j] = 0$。

2. 遍历数据集中的每个转移 $(S_t^{\text{target}}=i, S_t^{\text{hazard}}=h, S_{t+1}^{\text{target}}=j)$：

   -   $N[i,h,j] \mathrel{+}= 1$

3. 遍历每个状态对 (i,h)：

   - 计算总计数 $\text{total}$:
     $$
     \text{total} = \sum_j N[i,h,j]
     $$

   - 对于每个目标状态 j，应用平滑公式计算 $P_{\text{smooth}}[i,h,j]$:
     $$
     P_{\text{smooth}}[i,h,j] = \frac{N[i,h,j] + \alpha \cdot \pi[j]}{\text{total} + \alpha}
     $$

4. 返回 $P_{\text{smooth}}$。

### EM算法（混合马尔可夫模型）

**输入**：状态转移序列 $D$、分量数 $K$、最大迭代次数 $\text{max\_iter}$、收敛阈值 $\epsilon$
**输出**：混合权重参数 $\theta = \{\alpha_k, \beta_k, \gamma_k\}$、分量转移概率矩阵 $\{P_k\}$

1. 随机初始化参数 $\theta$ 和 $\{P_k\}$。

2. 对于 $\text{iter} = 1$ 到 $\text{max\_iter}$：

   - **E步骤**：计算责任 $r_{t,k}$

     - 对于每个数据点 t 和分量 k，计算给定数据点t由分量k生成的概率：
       $$
       r_{t,k} = \frac{w_k(h_t) \cdot P_k(S_{t+1}^{\text{target}}=j_t | S_t^{\text{target}}=i_t)}{\sum_l w_l(h_t) \cdot P_l(S_{t+1}^{\text{target}}=j_t | S_t^{\text{target}}=i_t)}
       $$

   - **M步骤**：更新参数

     - 更新转移概率矩阵：对于每个分量 k 和状态对 (i,j)，使用责任 $r_{t,k}$ 作为权重，对频次进行加权求和来更新 $P_k[i,j]$:
       $$
       P_k[i,j] = \frac{\sum_t r_{t,k} \cdot I(S_t^{\text{target}}=i, S_{t+1}^{\text{target}}=j)}{\sum_t r_{t,k} \cdot I(S_t^{\text{target}}=i)}
       $$

     - 更新混合权重参数：使用梯度下降法，沿着梯度的方向更新 $\theta$，以最大化期望的对数似然。($\eta$是学习率)
       $$
       \theta_{\text{new}} = \theta_{\text{old}} + \eta \cdot \nabla L(\theta)
       $$
       其中梯度 $\nabla L(\theta)$ 为：
       $$
       \nabla L(\theta) = \sum_t (r_{t,k} - w_k(h_t)) \cdot \nabla w_k(h_t)
       $$

   - 计算整体对数似然 $L$：
     $$
     L = \sum_t \log\left(\sum_k w_k(h_t) \cdot P_k(S_{t+1}^{\text{target}}=j_t | S_t^{\text{target}}=i_t)\right)
     $$

   - 检查收敛性：如果 $|L_{\text{new}} - L_{\text{old}}| < \epsilon$，则跳出循环。

3. 返回训练好的参数 $\theta$ 和 $\{P_k\}$。


### 隐马尔可夫模型参数估计（当系统状态不可直接观测时）

**详细解释**：
在某些情况下，系统的真实状态（如"设备内部微小损伤"）无法直接观测，我们只能观测到一些间接指标（如"设备振动异常"）。HMM就是用来处理这种情况的模型。它有三个核心参数：初始状态分布π，状态转移概率A，观测概率B（在某个真实状态下，观测到某个指标的概率）。

**输入**：观测序列 $O = \{O_1, ..., O_T\}$、灾害状态序列 $H = \{S_1^{\text{hazard}}, ..., S_T^{\text{hazard}}\}$
**输出**：状态转移概率矩阵 $A$、观测概率矩阵 $B$、初始状态分布 $\pi$

1. 随机初始化参数 $A, B, \pi$。

2. 执行Baum-Welch算法（EM算法在HMM中的特例），重复直至收敛：

   - **E步骤**：使用前向-后向算法计算

     - 前向概率 $\alpha_t(i)$: 到时间t为止，观测到序列 $O_1..O_t$，且真实状态为i的概率。
       $$
       \alpha_t(i) = P(O_1, ..., O_t, S_t^{\text{target}}=i | \lambda)
       $$

     - 后向概率 $\beta_t(i)$: 在时间t真实状态为i的条件下，观测到未来序列 $O_{t+1}..O_T$ 的概率。
       $$
       \beta_t(i) = P(O_{t+1}, ..., O_T | S_t^{\text{target}}=i, \lambda)
       $$

     - $\gamma_t(i)$: 在给定整个观测序列O的条件下，时间t的真实状态为i的概率。
       $$
       \gamma_t(i) = \frac{\alpha_t(i) \cdot \beta_t(i)}{\sum_j \alpha_t(j) \cdot \beta_t(j)}
       $$

     - $\xi_t(i,j)$: 在给定整个观测序列O的条件下，时间t状态为i，时间t+1状态为j的联合概率。
       $$
       \xi_t(i,j) = P(S_t^{\text{target}}=i, S_{t+1}^{\text{target}}=j | O, \lambda)
       $$

   - **M步骤**：基于E步的计算结果，重新估计参数

     - $\pi_i = \gamma_1(i)$  // 初始状态为i的概率等于t=1时状态为i的概率

     - 从状态i到j的转移概率 $A_{i,j}(h)$，等于所有从i到j的期望次数，除以所有从i出发的期望次数。
       $$
       A_{i,j}(h) = \frac{\sum_{t=1}^{T-1} \xi_t(i,j) \cdot I(S_t^{\text{hazard}}=h)}{\sum_{t=1}^{T-1} \gamma_t(i) \cdot I(S_t^{\text{hazard}}=h)}
       $$

     - 在状态i下观测到k的概率 $B_{i,k}$，等于在状态i的期望次数中，实际观测到k的次数所占比例。
       $$
       B_{i,k} = \frac{\sum_{t=1}^T \gamma_t(i) \cdot I(O_t=k)}{\sum_{t=1}^T \gamma_t(i)}
       $$

3. 返回 $A, B, \pi$。

## 实际应用案例示例

### 案例：电力系统在寒潮-雷击复合灾害下的状态预测

#### 状态空间定义

**解释**：将各种灾害和系统组件的状态进行离散化定义。
$$
\begin{align}
\text{寒潮状态} \ S_t^{\text{cold}} &= \{0,1,2,3\} \\
\text{雷击状态} \ S_t^{\text{lightning}} &= \{0,1,2,3\} \\
\text{风电状态} \ S_t^{\text{wind}} &= \{0,1,2,3\} \\
\text{光伏状态} \ S_t^{\text{PV}} &= \{0,1,2,3\} \\
\text{电网状态} \ S_t^{\text{grid}} &= \{0,1,2,3,4\}
\end{align}
$$

#### 转移概率矩阵示例（部分）

**解释**：这是一个一阶马尔可夫模型的转移概率向量示例。
$$
\begin{align}
P(S_{t+1}^{\text{wind}}=j | S_t^{\text{wind}}=1, S_t^{\text{cold}}=2, S_t^{\text{lightning}}=1) &= [0.25, 0.40, 0.30, 0.05] \\
P(S_{t+1}^{\text{PV}}=j | S_t^{\text{PV}}=1, S_t^{\text{cold}}=2, S_t^{\text{lightning}}=1) &= [0.15, 0.45, 0.35, 0.05]
\end{align}
$$
**说明**：第一行表示，在当前风电状态为1（轻微降低），寒潮为2级，雷击为1级时，下一时刻风电状态变为0（正常）、1（轻微降低）、2（显著降低）、3（严重降低）的概率分别是0.25, 0.40, 0.30, 0.05。

#### 混合马尔可夫模型参数（风电系统）

**解释**：这是一个训练好的混合模型权重函数的参数示例，假设有3个分量。
$$
\begin{align}
\alpha_1 &= 0.8, \ \beta_1^{\text{cold}} = 0.6, \ \beta_1^{\text{lightning}} = 0.2, \ \gamma_1 = 0.15 \\
\alpha_2 &= 0.3, \ \beta_2^{\text{cold}} = 0.3, \ \beta_2^{\text{lightning}} = 0.5, \ \gamma_2 = 0.25 \\
\alpha_3 &= -0.2, \ \beta_3^{\text{cold}} = 0.1, \ \beta_3^{\text{lightning}} = 0.3, \ \gamma_3 = 0.4
\end{align}
$$
**说明**：$\gamma$参数均为正，表示这三个分量都体现了寒潮和雷击的正向耦合（放大）效应。$\gamma_3=0.4$最大，说明第3个分量所代表的系统动态模式对耦合效应最敏感。

#### 预测结果示例

**解释**：展示在不同灾害情景下，模型如何给出不同的预测结果，体现了耦合效应的影响。
$$
\begin{align}
&\text{当前状态}：S_t^{\text{wind}}=1, \ S_t^{\text{cold}}=2, \ S_t^{\text{lightning}}=1 \\
&\text{预测结果}：P(S_{t+1}^{\text{wind}}) = [0.25, 0.40, 0.30, 0.05] \\
&\text{最可能下一状态}：S_{t+1}^{\text{wind}}=1（\text{轻微降低}）\\
\\
&\text{当前状态}：S_t^{\text{wind}}=1, \ S_t^{\text{cold}}=3, \ S_t^{\text{lightning}}=2 \\
&\text{预测结果}：P(S_{t+1}^{\text{wind}}) = [0.05, 0.18, 0.15, 0.62] \\
&\text{最可能下一状态}：S_{t+1}^{\text{wind}}=3（\text{严重降低}）
\end{align}
$$
**说明**：对比两种情况，当灾害等级从(寒潮=2, 雷击=1) 提升到 (寒潮=3, 雷击=2)时，系统最可能的下一状态从"轻微降低"急剧恶化为"严重降低"，并且严重降低的概率高达0.62。这清晰地展示了模型如何通过耦合效应对加剧的灾害做出更差的预测。

### 第二阶段：可再生能源功率波动→E1-失压或过载（发电厂上送第一级变电站的线路过载）

#### 电力系统潮流方程

==电力系统的潮流是指电网中各节点的电压和相角分布，以及各线路上的有功和无功功率流动情况。==

电力系统中各节点的有功、无功功率与节点电压幅值、相角之间满足如下潮流方程：

$$
\begin{cases}
P_k = V_k \sum_{m=1}^N V_m \left[ G_{km} \cos(\theta_k - \theta_m) + B_{km} \sin(\theta_k - \theta_m) \right] \\
Q_k = V_k \sum_{m=1}^N V_m \left[ G_{km} \sin(\theta_k - \theta_m) - B_{km} \cos(\theta_k - \theta_m) \right]
\end{cases}
$$

其中：

- $P_k, Q_k$：节点 $k$ 的有功、无功注入
- $V_k, V_m$：节点 $k, m$ 的电压幅值
- $\theta_k, \theta_m$：节点 $k, m$ 的电压相角
- $G_{km}, B_{km}$：节点导纳矩阵的实部、虚部

**解释**：潮流方程是电网运行分析的基础，节点电压灵敏度、线路负载率等均可由其推导获得。

---

#### 线路负载率模型

线路 $l$ 的负载率定义为：

==视在功率==
$$
LR_l(t) = \frac{S_{line,l}(t)}{S_{rated,l}} \times 100\%
$$

风电和光伏功率变化引起的线路潮流变化：
$$
\Delta S_{line,l}(t) = \sqrt{\left(\Delta P_{l,total}(t)\right)^2 + \left(\Delta Q_{l,total}(t)\right)^2}
$$

其中：
$$
\Delta P_{l,total}(t) = \sum_{i=1}^{n_W} PTDF_{i,l}^{W,P} \cdot [P_i^W - P_i^{W'}(t)] + \sum_{j=1}^{n_{PV}} PTDF_{j,l}^{PV,P} \cdot [P_j^{PV} - P_j^{PV'}(t)]
$$

$$
\Delta Q_{l,total}(t) = \sum_{i=1}^{n_W} PTDF_{i,l}^{W,Q} \cdot [Q_i^W - Q_i^{W'}(t)] + \sum_{j=1}^{n_{PV}} PTDF_{j,l}^{PV,Q} \cdot [Q_j^{PV} - Q_j^{PV'}(t)]
$$

**解释**：线路负载率表示线路实际传输功率与额定容量的比值，PTDF(功率传输分布因子)表示风电和光伏功率变化对线路潮流的影响系数。

---

#### 光伏电站对线路功率贡献计算

光伏电站对线路 $l$ 的有功功率贡献：

$$
P_{l,pv}(t) = \sum_{j=1}^{n_{PV}} M_{jl}^{PV} \cdot P_j^{PV'}(t) \cdot PTDF_{j,l}^{PV,P}
$$

光伏电站对线路 $l$ 的无功功率贡献：

$$
Q_{l,pv}(t) = \sum_{j=1}^{n_{PV}} M_{jl}^{PV} \cdot P_j^{PV'}(t) \cdot \tan(\phi_j^{PV}) \cdot PTDF_{j,l}^{PV,Q}
$$

其中：

- $M_{jl}^{PV}$：光伏电站 $j$ 与线路 $l$ 的连接关系
- $P_j^{PV'}(t)$：光伏电站 $j$ 在时间 $t$ 的实际出力
- $PTDF_{j,l}^{PV,P}$和$PTDF_{j,l}^{PV,Q}$：光伏电站功率变化对线路潮流的影响系数
- $\tan(\phi_j^{PV})$：光伏电站 $j$ 的无功/有功比

---

#### 线路总实际功率计算

对于线路 $l$，其在时间 $t$ 的实际功率包含风电、光伏和其他电源的贡献：


$$
S_{line,l}(t) = \sqrt{\left(P_{l,wind}(t) + P_{l,pv}(t) + P_{l,other}(t)\right)^2 + \left(Q_{l,wind}(t) + Q_{l,pv}(t) + Q_{l,other}(t)\right)^2}
$$

---

#### E1故障(失压或过载)概率模型

$$
E1_{prob}(t) = 1 - \prod_{l=1}^{L} \left(1 - \Phi\left(\frac{LR_l(t) - 100}{\sigma_{LR,l}}\right)\right)
$$

其中 $\Phi$ 是标准正态分布的累积分布函数， $\sigma_{LR,l}$ 是负载率的标准差参数。

**解释**：由于考虑了风电和光伏的共同影响，E1故障的概率更准确地反映了系统中线路负载率超过100%的可能性。

# 基于PTDF的风光发电过载调度策略模型

## 引言：风光发电过载场景及调度意义

随着可再生能源比例的增加，风电、光伏大发等情况下导致的线路过载问题日益凸显。当风力条件良好或日照充足时，风电场或光伏电站大发电会导致送出线路负载超过安全阈值，需要调度中心及时干预。本文建立基于PTDF的风光发电厂线路过载调度策略模型，用于指导调度中心快速响应并解决过载问题。

**调度的重要性：**

- **保障电网安全**：过载线路如不及时处理，可能导致线路跳闸，引发更严重的连锁故障
- **最大化可再生能源消纳**：通过精准调度，在确保电网安全的前提下最大限度利用风光资源
- **提高系统经济性**：采用优化的调度策略，最小化发电站的出力损失

## 风光并网线路过载检测模型

### 线路负载率计算

线路负载率是衡量线路过载程度的关键指标，定义为线路实际传输的视在功率与其额定容量之比：

$$
LR_l(t) = \frac{P_{line,l}(t)}{P_{rated,l}} \times 100\%
$$

其中：==（视在功率换成有功功率）==

- $LR_l(t)$ 表示线路 $l$ 在时间 $t$ 的负载率（百分比）
- $P_{line,l}(t)$ 表示线路 $l$ 在时间 $t$ 的视在功率（MVA）
- $P_{rated,l}$ 表示线路 $l$ 的额定容量（MVA）

当 $LR_l(t) > 100\%$ 时，表示线路处于过载状态，需要采取调度措施。

### 新能源送出线路视在功率计算

新能源（风电、光伏）送出线路的视在功率计算如下：

$$
S_{line,l}(t) = \sqrt{P_{line,l}^2(t) + Q_{line,l}^2(t)}
$$

其中：

- $P_{line,l}(t)$ 为线路 $l$ 在时间 $t$ 的有功功率
- $Q_{line,l}(t)$ 为线路 $l$ 在时间 $t$ 的无功功率

### 过载检测阈值

考虑到线路短时过载能力和测量误差，设定分级过载检测阈值：

$$
LR_{threshold} = \begin{cases}
100\% + \delta_1, & \text{轻度过载} \\
100\% + \delta_2, & \text{中度过载} \\
100\% + \delta_3, & \text{重度过载}
\end{cases}
$$

其中 $\delta_1 < \delta_2 < \delta_3$ 为不同级别的裕度系数，通常取 $\delta_1 = 5\%$, $\delta_2 = 10\%$, $\delta_3 = 15\%$。

当 $LR_l(t) > 100\% + \delta_1$ 时，触发过载调度流程。

## 基于PTDF的风光发电过载调度模型

### PTDF基本概念与意义

PTDF（Power Transfer Distribution Factor，功率传输分布因子）是描述**电网中某条线路的功率潮流对某个节点注入功率变化的灵敏度**。PTDF矩阵是进行精确过载调度的核心技术基础。

对于由n个节点和m条线路组成的电网，PTDF矩阵维度为m×n：

$$
PTDF = \begin{bmatrix}
PTDF_{1,1} & PTDF_{1,2} & \cdots & PTDF_{1,n} \\
PTDF_{2,1} & PTDF_{2,2} & \cdots & PTDF_{2,n} \\
\vdots & \vdots & \ddots & \vdots \\
PTDF_{m,1} & PTDF_{m,2} & \cdots & PTDF_{m,n}
\end{bmatrix}
$$

其中，$PTDF_{l,s}$表示节点s的功率注入变化对线路l的功率流影响系数：

$$
PTDF_{l,s} = \frac{\Delta S_l}{\Delta P_s}
$$

这里：

- $\Delta S_l$ 表示线路l的视在功率变化量（MVA）
- $\Delta P_s$ 表示节点s的有功注入功率变化量（MW）

PTDF值的物理意义是：==节点s注入功率增加1MW时，线路l的功率流增加$PTDF_{l,s}$MVA。==

### PTDF矩阵计算方法

实际应用中，PTDF矩阵主要通过以下两种方法计算：

#### 直流潮流法（简化法）==物理意义==

基于直流潮流模型的简化假设（忽略线路电阻，假设节点电压恒定，相角差较小），PTDF矩阵计算公式为：

$$
PTDF = [X_d]^{-1} \cdot A \cdot [B']
$$

其中：

- $X_d$ 是线路电抗对角矩阵
- $A$ 是线路-节点关联矩阵
- $B'$ 是节点导纳矩阵的广义逆矩阵

#### 交流潮流敏感度法（精确法）

考虑交流潮流的全部特性，使用雅可比矩阵求解：

==相角==是什么？
$$
PTDF_{l,s} = \frac{\partial S_l}{\partial P_s} = \frac{\partial S_l}{\partial \theta} \cdot \frac{\partial \theta}{\partial P_s} + \frac{\partial S_l}{\partial V} \cdot \frac{\partial V}{\partial P_s}
$$

==这个公式怎么计算，描述整个过程==

计算步骤：

1. **计算初始潮流**：求解电网初始状态下的节点电压和相角
2. **形成雅可比矩阵$J$**：构建电力系统状态变量与功率注入的敏感度矩阵
3. **计算功率注入敏感度**：求解功率注入对节点电压和相角的影响
4. **计算潮流敏感度**：求解线路潮流对节点电压和相角的敏感度
5. **组合得到PTDF值**：通过链式法则计算综合敏感度

### 基于PTDF的调度目标与约束

风光发电过载调度的主要目标是通过调整发电站出力，使过载线路的负载率降至安全水平，同时尽量==减小==对==发电站发电量==的影响：

$$
\min \sum_{s=1}^{S} C_s \cdot \Delta P_s^{down}
$$

其中：

- $C_s$ 为发电站 $s$ 的调整成本系数或优先级
- $\Delta P_s^{down}$ 为发电站 $s$ 需要降低的出力(风机切断，光伏设置逆变器)

约束条件：对于所有处于过载状态的线路，其负载率必须被调度到不超过安全阈值：

$$
s.t. \quad LR_l(t) \leq LR_{safe} \quad \forall l \in L_{overload}
$$

其中：

- $LR_{safe}$ 为安全负载率，通常取95%
- $L_{overload}$ 为过载线路集合

### 基于PTDF的调度流程

PTDF调度流程是一个系统性的过程，包含以下关键步骤：

1. **电网数据准备**：
   - 收集电网拓扑结构信息
   - 获取线路参数和额定容量
   - 记录发电节点和负荷节点信息

2. **PTDF矩阵计算**：
   - 根据电网结构和参数计算完整PTDF矩阵
   - 验证PTDF矩阵准确性==（可选项）==

3. **过载线路识别**：
   - 实时监测线路负载率
   - 识别负载率超过阈值的线路

4. **发电站筛选**：
   - 形成候选调整集合：$S_l = \{s | |PTDF_{l,s}| > \varepsilon\}$
   - 其中$\varepsilon$是预设的PTDF阈值，用于筛选对过载线路有显著影响的节点

5. **调整量计算**：
   - 基于PTDF值和当前出力计算各发电站的调整量

6. **调度指令执行**：
   - 向相关发电站下发调度指令
   - 监测调度效果，必要时进行二次调整

### 基于PTDF的出力调整量计算

发电站 $s$ 需要减少的出力计算公式：

$$
\Delta P_s^{down} = \alpha_s \cdot \frac{(LR_l(t) - LR_{safe}) \cdot S_{rated,l}}{PTDF_{l,s}} \cdot \frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}
$$

其中：

- $\alpha_s$ 为发电站 $s$ 的调整系数，反映其调节优先级
- $(LR_l(t) - LR_{safe}) \cdot S_{rated,l}$ 表示需要减少的线路传输功率
- $PTDF_{l,s}$ 表示发电站连接节点对线路的影响系数
- $\frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}$ 表示按发电站当前出力比例分配削减量
- $P_s(t)$是发电站$s$的功率

**公式组成部分详解**

1. **过载程度量化**：$(LR_l(t) - LR_{safe}) \cdot S_{rated,l}$
   - 这部分计算线路实际需要减少的功率量（MVA）
   - 例如：线路负载率为115%，安全阈值为95%，额定容量为300MVA
   - 需减少功率 = (115% - 95%) × 300MVA = 60MVA

2. **功率转换系数**：$\frac{1}{PTDF_{l,s}}$
   - PTDF值表示节点功率变化对线路功率的影响系数
   - 除以PTDF将"线路上需减少的功率"转换为"节点上需减少的功率"
   - ==对于PTDF值高的节点，同样的节点功率调整能产生更大的线路功率变化==
   - 例如：PTDF值为0.75的发电站，需调整80MW才能使线路功率减少60MVA (60÷0.75=80)

3. **负担分配因子**：$\frac{P_s(t)}{\sum_{s' \in S_l} P_{s'}(t)}$
   - 这部分实现了按发电站出力比例分配调整责任
   - 出力较大的发电站承担更多调整量，体现公平性
   - 例如：发电站A出力300MW，集合内总出力为750MW，则分担比例为40%

4. **调整系数**：$\alpha_s$==（自定义）==
   - 用于微调各发电站的调整比例，引入灵活性
   - 可基于经济性、技术特性、环保价值等因素设定
   - 例如：对灵活性高的风电场可设α=1.2，对光伏电站可设α=0.9

该公式巧妙地结合了两个关键因素：

- **技术效率**：通过PTDF值反映调整效率，优先利用高效率节点
- **公平分配**：通过出力比例分配调整负担，避免单一发电站承担全部责任。

### 发电站调整方案实施方式

根据实际需求，发电站调整出力的实施方式有多种：

1. **手动控制方式**：调度中心下发指令要求发电站降低出力，发电站自行决定具体的实施方案
2. **AGC远程控制方式**：调度中心通过AGC系统直接向发电站下发遥控指令

发电站内部出力调整计算：

**风电场**：计算需要切除的风机数量
$$
N_{cut,wind} = \left\lceil \frac{\Delta P_w^{down}}{P_{unit,wind}} \right\rceil
$$

**光伏电站**：设定逆变器总输出功率上限
$$
P_{pv,new\_setpoint} = P_{pv,current} - \Delta P_{pv}^{down}
$$

## 基于PTDF的风光发电过载调度案例分析

### 复杂电网单线路过载调度案例

考虑一个区域电网中的单线路过载问题，该区域包含4个发电节点（2个风电场、2个光伏电站）和1条主要过载线路：

**发电节点信息**：

- 风电场A：连接节点1，装机容量350MW，当前出力320MW，包含140台单机容量2.5MW的风机
- 风电场B：连接节点2，装机容量250MW，当前出力230MW，包含92台单机容量2.5MW的风机
- 光伏电站C：连接节点3，装机容量200MW，当前出力180MW，逆变器效率98%
- 光伏电站D：连接节点4，装机容量150MW，当前出力130MW，逆变器效率97%

**线路信息**：

- 线路L1：节点1→节点5→节点6，额定容量400MVA，当前负载率118%，安全负载率95%
- 线路参数：长度85公里，电阻0.028Ω/km，电抗0.315Ω/km，电容236nF/km

**PTDF值**：
基于详细的电网潮流计算，得到各发电站对线路L1的PTDF值：

- 风电场A对线路L1的PTDF值：0.72
- 风电场B对线路L1的PTDF值：0.38
- 光伏电站C对线路L1的PTDF值：0.25
- 光伏电站D对线路L1的PTDF值：0.12

### 详细调度计算过程

#### 线路过载分析

当前线路L1负载率为118%，超过安全阈值95%。计算过载功率：
$\Delta S_{L1} = (118\% - 95\%) \cdot 400MVA = 92MVA$

线路实际功率：$S_{L1} = 118\% \cdot 400MVA = 472MVA$

通过潮流计算，分解为有功和无功：

- 有功功率：$P_{L1} = 456MW$
- 无功功率：$Q_{L1} = 125Mvar$

功率因数：$\cos\phi = \frac{P_{L1}}{S_{L1}} = \frac{456}{472} = 0.966$

#### 候选调整集合确定

设定PTDF阈值$\varepsilon = 0.15$，形成候选调整集合：==（选择策略怎么确定，阈值怎么设置）==
$S_{L1} = \{s | |PTDF_{l,s}| > 0.15\} = \{A, B, C\}$

光伏电站D的PTDF值为0.12，低于阈值，不纳入候选调整集合。

#### 调整系数确定

考虑各发电站的技术特性、调整灵活性和经济性，设定调整系数：==(加人工智能)==

- 风电场A：$\alpha_A = 1.05$（调整灵活性较高）
- 风电场B：$\alpha_B = 1.00$（基准值）
- 光伏电站C：$\alpha_C = 0.90$（调整成本较高）

#### 各发电站调整量计算

**风电场A调整量**：
$\Delta P_A = \alpha_A \cdot \frac{92}{0.72} \cdot \frac{320}{320+230+180} = 1.05 \cdot \frac{92}{0.72} \cdot \frac{320}{730} = 1.05 \cdot 127.78 \cdot 0.438 = 58.82MW$

**风电场B调整量**：
$\Delta P_B = \alpha_B \cdot \frac{92}{0.38} \cdot \frac{230}{320+230+180} = 1.00 \cdot \frac{92}{0.38} \cdot \frac{230}{730} = 1.00 \cdot 242.11 \cdot 0.315 = 76.26MW$

**光伏电站C调整量**：
$\Delta P_C = \alpha_C \cdot \frac{92}{0.25} \cdot \frac{180}{320+230+180} = 0.90 \cdot \frac{92}{0.25} \cdot \frac{180}{730} = 0.90 \cdot 368 \cdot 0.247 = 81.75MW$

#### 考虑技术约束的调整量优化

**风电场A**：
最小可调整单元为单台风机，单机容量2.5MW
最接近的可调整风机数量：$N_{cut,A} = \lceil \frac{58.82}{2.5} \rceil = 24$台
实际调整量：$\Delta P_A^{actual} = 24 \cdot 2.5 = 60MW$

**风电场B**：
最小可调整单元为单台风机，单机容量2.5MW
最接近的可调整风机数量：$N_{cut,B} = \lceil \frac{76.26}{2.5} \rceil = 31$台
实际调整量：$\Delta P_B^{actual} = 31 \cdot 2.5 = 77.5MW$

**光伏电站C**：
光伏可以连续调整逆变器输出功率
实际调整量：$\Delta P_C^{actual} = 81.75MW$

#### 调度指令执行方案

**风电场A执行方案**：

1. 根据风机位置和风速，选择24台位于风场外围且风速相对较低的风机
2. 发送远程控制指令，将这些风机切出运行
3. 实时监测风场总出力，确认降至目标值260MW（320MW-60MW）
4. 启动AGC自动功率控制，维持该出力水平

**风电场B执行方案**：

1. 按照预设的调度规则，选择31台风机进行切除
2. 分两批执行切除指令，每批间隔30秒，避免系统冲击
3. 新设定点为152.5MW（230MW-77.5MW）
4. 启动风场出力监控系统，确保调整效果

**光伏电站C执行方案**：

1. 通过中央控制系统向全部逆变器发送功率限制指令
2. 将逆变器总输出功率上限设为98.25MW（180MW-81.75MW）
3. 监测输出功率变化曲线，确保平稳过渡
4. 根据光照强度变化，动态调整限功率水平

#### 调度效果预测与验证

调度实施后预期的线路功率减少量：
$\Delta S_{L1} = 60MW \cdot 0.72 + 77.5MW \cdot 0.38 + 81.75MW \cdot 0.25 = 43.2 + 29.45 + 20.44 = 93.09MVA$

预期的新线路负载率：
$LR_{L1}^{new} = \frac{472MVA - 93.09MVA}{400MVA} \cdot 100\% = \frac{378.91}{400} \cdot 100\% = 94.73\%$

结果表明，执行调度方案后，线路L1的负载率将降至安全水平以下。

---

### 第三阶段：E1故障→O3-220KV线路影响→系统断面形成

#### O3-220kV线路影响概率模型（人工智能做）

重要性权重->电力安全事故事件发生的概率与后果（不同级别电力事件对应不同的权重，可以上网查到）
$$
O3_{prob}(t) = E1_{prob}(t) \cdot SIF_{O3}(t)
$$

其中 $SIF_{O3}(t)$ 是系统影响因子，表示E1故障传播到O3-220KV线路的概率，可通过==（网络拓扑和线路特性，AI）==计算：

$$
SIF_{O3}(t) = 1 - \exp\left(-\eta \cdot \sum_{l \in E1} \frac{W_l \cdot LR_l(t)}{d_{l,O3}}\right)
$$

==$W_l$ 是线路 $l$ 的重要性权重==，$d_{l,O3}$ 是线路 $l$ 的电气距离，$\eta$ 是影响传播系数。

$d_{l,O3}$ 是基于系统拓扑和电气参数计算的"电气距离"，它反映的是==故障从线路 $l$ 传导到 O3 的"难易程度"==。

- $O3_{prob}(t)$：时刻$t$，O3-220KV线路系统受到影响（如过载、失压、故障等）的概率。

- $E1_{prob}(t)$：时刻$t$，系统内发生E1类故障（关键节点失压或关键线路过载）的概率。

- $SIF_{O3}(t)$：系统影响因子（System Impact Factor），表示E1故障发生后，影响能否、以及多大程度上传导到O3-220KV线路系统的概率。

**解释**：O3-220KV线路受影响的概率取决于E1故障的发生概率，以及故障从E1传播到O3的条件概率，后者与系统结构、线路负载水平及电气距离有关。

权重与220kV线路的联系：

系统级联关系：在电力系统中，不同电压等级的线路是相互连接的。E1故障可能最初发生在连接风电场或光伏电站的线路上，但这些故障可以通过电网逐步传播到220kV线路（O3系统）。

权重表示故障传播可能性：$W_l$ 权重反映了特定线路 $l$ 的故障传播到O3-220kV线路系统的相对可能性。权重越大，表示该线路故障更容易影响到220kV线路系统。

网络拓扑决定权重：权重大小取决于线路在网络中的位置和重要性。

例如：

- 直接连接到220kV变电站的线路权重可能较高

- 为220kV系统提供大量电力输入的线路权重可能较高

- 连接关键发电设施的线路权重可能较高

电气距离影响：公式中的 $d_{l,O3}$ 表示线路 $l$ 到O3系统的电气距离。电气距离近的线路，其故障更容易传播到O3系统，因此权重的实际影响会更大。

简单来说，权重系统建立了一种从初始故障点（可能是较低电压等级线路）到目标系统（220kV线路系统）的影响映射关系，帮助量化不同线路故障对整个系统的潜在危害程度。

（概率抽象成级别）

---

#### 风电场与输电线路对应关系模型

为了更精确地描述寒潮从风机影响到线路故障的传导机制，建立风电场与输电线路的一一对应关系：

$$
L = \{l_1, l_2, ..., l_m\}
$$

其中 $L$ 表示广州市输电网络中的所有线路集合，包含 $m$ 条220KV线路。

引入对应矩阵 $M_{il}^W$：

$$
M_{il}^W = 
\begin{cases}
1, & \text{风电场 } i \text{ 的输出电力通过线路 } l \text{ 传输} \\
0, & \text{其他情况}
\end{cases}
$$

**解释**：矩阵 $M_{il}^W$ 表示风电场与输电线路的拓扑连接关系，确定每个风电场的出力通过哪些线路传输。

---

#### 线路集成模型

220KV输电线路系统是由多条单一线路组成的复杂网络：
$$
O3_{total} = \{l_1, l_2, ..., l_q\}
$$

其中 $O3_{total}$ 表示构成O3-220KV线路系统的 $q$ 条子线路集合。

各子线路参数：

| 线路编号 | 起始节点 | 终止节点 | 额定容量 (MVA) | 长度 (km) | 阻抗参数 (p.u.) |
| -------- | -------- | -------- | -------------- | --------- | --------------- |
| $l_1$    | Bus-A    | Bus-B    | $S_{rated,1}$  | $D_1$     | $Z_1$           |
| $l_2$    | Bus-B    | Bus-C    | $S_{rated,2}$  | $D_2$     | $Z_2$           |
| $\vdots$ | $\vdots$ | $\vdots$ | $\vdots$       | $\vdots$  | $\vdots$        |
| $l_q$    | Bus-Y    | Bus-Z    | $S_{rated,q}$  | $D_q$     | $Z_q$           |

**变量说明**：

- $l_1, l_2, ..., l_q$：O3-220KV线路系统中的各条子线路编号。
- $S_{rated,l}$：第 $l$ 条线路的额定容量，单位为MVA。
- $D_l$：第 $l$ 条线路的长度，单位为km。
- $Z_l$：第 $l$ 条线路的阻抗参数，单位为标幺(p.u.)。阻抗参数 $Z$ 是"电的流动阻力"，它决定了电流在输电线路中传输时有多"难"，影响功率分布、能量损耗和故障传播。
- 起始节点/终止节点：线路连接的母线编号。

**建模理由**：

- 通过详细列出每条子线路的参数，可以反映O3-220KV线路系统的物理结构和电气特性，为后续的功率分配、负载率计算和风险评估提供基础数据。
- 这些参数直接影响线路的承载能力、潮流分布和故障传播路径。

**工程意义**：

- 便于识别系统中的薄弱环节和关键线路，为调度优化和应急预案制定提供依据。
- 有助于后续模型中对各线路风险、断面形成等环节的精细化分析。

---

#### 分线路功率计算模型

对于线路 $l$，其在时间 $t$ 的实际功率为风电场的贡献与其他电源的贡献之和：

$$
S_{line,l}(t) = \sqrt{\left(P_{l,wind}(t) + P_{l,pv}(t) + P_{l,other}(t)\right)^2 + \left(Q_{l,wind}(t) + Q_{l,pv}(t) + Q_{l,other}(t)\right)^2}
$$

其中：

- $P_{l,wind}(t)$：风电场对线路 $l$ 在时间 $t$ 的有功功率贡献。
- $P_{l,pv}(t)$：光伏电站对线路 $l$ 在时间 $t$ 的有功功率贡献。
- $P_{l,other}(t)$：其他电源对线路 $l$ 的有功功率贡献。
- $Q_{l,wind}(t)$：风电场对线路 $l$ 的无功功率贡献。
- $Q_{l,pv}(t)$：光伏电站对线路 $l$ 的无功功率贡献。
- $Q_{l,other}(t)$：其他电源对线路 $l$ 的无功功率贡献。

风电场对线路 $l$ 的有功功率贡献：

$$
P_{l,wind}(t) = \sum_{i=1}^{n} M_{il}^W \cdot P_i^{W'}(t) \cdot PTDF_{i,l}^{W,P}
$$

- $M_{il}^W$：风电场 $i$ 与线路 $l$ 的连接关系（1为连接，0为不连接）。
- $P_i^{W'}(t)$：风电场 $i$ 在时间 $t$ 的实际出力。
- $PTDF_{i,l}^{W,P}$：风电场 $i$ 有功功率变化对线路 $l$ 潮流的分布因子。

风电场对线路 $l$ 的无功功率贡献：

$$
Q_{l,wind}(t) = \sum_{i=1}^{n} M_{il}^W \cdot P_i^{W'}(t) \cdot \tan(\phi_i^W) \cdot PTDF_{i,l}^{W,Q}
$$

- $\tan(\phi_i^W)$：风电场 $i$ 的无功/有功比。
- $PTDF_{i,l}^{W,Q}$：风电场 $i$ 无功功率变化对线路 $l$ 潮流的分布因子。

**建模理由**：

- 该模型细致刻画了风电场和其他电源对每条线路的有功、无功功率贡献，反映了电网中功率流动的实际分布。
- 通过PTDF因子，能够量化风电波动对各线路潮流的影响，为风险评估和调度优化提供依据。

**工程意义**：

- 有助于识别因风电波动导致的局部线路过载风险。
- 支持分线路的动态监控和分级预警。

---

#### 线路负载率与爬坡风险等级对应关系

建立线路负载率与爬坡风险等级的映射：

$$
Risk_{line,l}(t) = 
\begin{cases}
\text{低风险}, & LR_l(t) \leq 70\% \\
\text{中等风险}, & 70\% < LR_l(t) \leq 85\% \\
\text{高风险}, & 85\% < LR_l(t) \leq 100\% \\
\text{极高风险}, & LR_l(t) > 100\%
\end{cases}
$$

- $LR_l(t)$：线路 $l$ 在时间 $t$ 的负载率。

进一步，将爬坡风险与线路风险建立关联：

$$
Corr_{l}(Risk_{ramp}, Risk_{line}) = 
\begin{pmatrix}
p_{11} & p_{12} & p_{13} & p_{14} \\
p_{21} & p_{22} & p_{23} & p_{24} \\
p_{31} & p_{32} & p_{33} & p_{34} \\
p_{41} & p_{42} & p_{43} & p_{44} \\
\end{pmatrix}
$$

- $p_{ij}$：爬坡风险在级别 $i$ 时，线路风险达到级别 $j$ 的条件概率。

**建模理由**：

- 通过分级设定负载率阈值，便于调度员快速识别高风险线路。
- 概率转移矩阵反映了爬坡事件对线路风险的影响规律。

**工程意义**：

- 支持分级预警和动态风险管理。
- 为风电调度和应急响应提供决策依据。

---

#### 系统断面扰动模型

==假设断面恒定==

夏季和冬季对应于大负荷小负荷，两种情景，大小负荷对应不同的断面安全阈值范围。F（X）

电流矢量和，也可以用功率来考量。

发生断面

1. 原断面失效，调节未故障的电流
2. 不能调节，就划分新的断面

当多条220KV线路同时受到严重影响时，断面发生扰动的概率：

$$
Section_{prob}(t) = 1 - \prod_{l \in O3_{critical}} (1 - O3_{prob,l}(t))
$$

- $O3_{critical}$：O3线路系统中的关键线路集合。
- $O3_{prob,l}(t)$：线路 $l$ 在时间 $t$ 受影响的概率。
- $Section_{threshold}$：系统断面风险警戒阈值。
- $1 - O3_{prob,l}(t)$：线路 $l$ 在时间 $t$ 保持正常运行的概率
- $\prod_{l \in O3_{critical}} (1 - O3_{prob,l}(t))$：==所有关键线路同时保持正常运行的联合概率==
- $Section_{prob}(t)$：时间 $t$ 系统发生断面的概率（至少一条关键线路受到影响）
- $Section_{prob}(t) \geq Section_{threshold}$：触发断面风险预警或应急策略的判断条件

整个表达式是"==至少一条关键线路出问题"的概率。

**建模理由**：

- 采用联合概率模型，反映多条关键线路同时受影响时系统分裂的风险。
- 便于量化断面形成的概率，为系统防御和恢复策略提供依据。

**工程意义**：

- 预警系统分裂风险，指导断面防御措施的启动时机。
- 支持孤岛运行、负荷切除等应急策略的制定。

---

### 总线路功率汇总模型

将各分线路功率汇总，计算整个O3-220KV线路系统的总功率：

$$
S_{O3,total}(t) = \sum_{l \in O3_{total}} S_{line,l}(t)
$$

功率分配比例：

$$
Ratio_l(t) = \frac{S_{line,l}(t)}{S_{O3,total}(t)} \times 100\%
$$

- $S_{O3,total}(t)$：O3-220KV线路系统在时间 $t$ 的总视在功率。
- $S_{line,l}(t)$：第 $l$ 条线路的视在功率。
- $Ratio_l(t)$：第 $l$ 条线路在O3系统中的功率分配比例。

**建模理由**：

- 汇总各线路功率，反映系统整体负载水平。
- 计算分配比例，便于识别负载分布不均和局部过载风险。

**工程意义**：

- 支持系统级负载监控和优化。
- 为调度分流、负载转移等措施提供数据支撑。

---

### 爬坡事件风险等级评估

$$
Risk_{ramp}(t_1, t_2) = 
\begin{cases}
\text{低风险}, & r(t_1, t_2) \leq 0.6 \cdot r_{thres} \\
\text{中等风险}, & 0.6 \cdot r_{thres} < r(t_1, t_2) \leq 0.8 \cdot r_{thres} \\
\text{高风险}, & 0.8 \cdot r_{thres} < r(t_1, t_2) \leq r_{thres} \\
\text{极高风险}, & r(t_1, t_2) > r_{thres}
\end{cases}
$$

- $r(t_1, t_2)$：观测时段 $[t_1, t_2]$ 内的爬坡率。
- $r_{thres}$：系统可承受的爬坡率阈值。

**建模理由**：

- 通过分级设定爬坡率阈值，便于动态评估风电波动对系统的冲击。
- 反映不同强度爬坡事件对系统安全的威胁程度。

**工程意义**：

- 支持分级预警和调度响应。
- 为风电场功率控制和储能调度提供依据。

---

### 整体受影响程度

$$
I_{\text{total}}^W = \frac{\sum_{i=1}^{n} P_i^W - \sum_{i=1}^{n} P_i^{W'}(t)}{\sum_{i=1}^{n} P_i^W} \times 100\%
$$

- $P_i^W$：风电场 $i$ 的额定出力。
- $P_i^{W'}(t)$：风电场 $i$ 受寒潮影响后的实际出力。
- $I_{\text{total}}^W$：全市风电总损失占比。

**建模理由**：

- 量化寒潮事件对全市风电系统的整体冲击。
- 便于横向对比不同寒潮事件或不同防控措施下的系统损失。

**工程意义**：

- 为宏观调度和政策制定提供依据。
- 支持风电系统韧性评估和优化。

---

### 风机风险评分

$$
R_i^W = I_{ij}^W(t) \cdot P_i^W
$$

- $I_{ij}^W(t)$：寒潮 $j$ 在时间 $t$ 对区域 $i$ 风机的影响程度。
- $P_i^W$：区域 $i$ 风机的额定功率。
- $R_i^W$：风机区域的风险评分。

**建模理由**：

- 综合考虑风机受寒潮影响的严重程度和容量，量化各区域风险。
- 便于排序和优先调度高风险区域。

**工程意义**：

- 支持风电场分区调度和差异化防控。
- 为资源优化配置和应急响应提供参考。

# 断面风险评估与应急措施

## 断面风险评估方法

### 断面定义与表示

断面是电力系统中将网络分割成两个或多个区域的一组输电线路集合。在数学上，我们可以用矩阵形式表示断面。
$$
\mathbf{S} = 
\begin{bmatrix}
s_{11} & s_{12} & \cdots & s_{1n} \\
s_{21} & s_{22} & \cdots & s_{2n} \\
\vdots & \vdots & \ddots & \vdots \\
s_{m1} & s_{m2} & \cdots & s_{mn}
\end{bmatrix}
$$

其中：

- $m$ 是系统中的断面数量
- $n$ 是系统中的线路总数
- $s_{ij} = 1$ 表示线路 $j$ 属于断面 $i$
- $s_{ij} = 0$ 表示线路 $j$ 不属于断面 $i$

### 断面传输能力

断面 $k$ 的传输能力定义为：

$$
P_{k,max} = \sum_{j=1}^{n} s_{kj} \cdot P_{j,max}
$$

其中：

- $P_{k,max}$ 是断面 $k$ 的最大安全传输能力
- $P_{j,max}$ 是线路 $j$ 的最大传输容量
- $s_{kj}$ 表示线路 $j$ 是否属于断面 $k$

### 断面实际传输功率

断面 $k$ 在时间 $t$ 的实际传输功率：

$$
P_{k,actual}(t) = \sum_{j=1}^{n} s_{kj} \cdot P_j(t)
$$

其中 $P_j(t)$ 是线路 $j$ 在时间 $t$ 的实际传输功率。

### 断面负载率

断面 $k$ 的负载率定义为：

$$
LR_k(t) = \frac{P_{k,actual}(t)}{P_{k,max}} \times 100\%
$$

### 断面风险评估指标

1. **负载率**：
   $$
   Risk_{load,k}(t) = 
   \begin{cases}
   \text{低负载率}, & LR_k(t) \leq 70\% \\
   \text{中等风险}, & 70\% < LR_k(t) \leq 85\% \\
   \text{高风险}, & 85\% < LR_k(t) \leq 95\% \\
   \text{极高风险}, & LR_k(t) > 95\%
   \end{cases}
   $$

2. **线路状态**：
   $$
   Risk_{state,k}(t) = 1 - \prod_{j=1}^{n} (1 - s_{kj} \cdot O3_{prob,j}(t))
   $$

   其中，$O3_{prob,j}(t)$ 是线路 $j$ 在时间 $t$ 发生故障的概率。

3. **综合风险指标**：
   $$
   Risk_{total,k}(t) = w_1 \cdot Risk_{load,k}(t) + w_2 \cdot Risk_{state,k}(t)
   $$

   其中，$w_1$ 和 $w_2$ 是权重系数，满足 $w_1 + w_2 = 1$。

### 断面安全阈值动态调整（可变或者不可变）

考虑到夏季(大负荷)和冬季(小负荷)的差异，断面安全阈值可动态调整：

$$
P_{k,max}(t) = P_{k,max,base} \cdot f(L(t), T(t))
$$

其中：

- $P_{k,max,base}$ 是基准最大传输能力
- $L(t)$ 是系统负荷水平
- $T(t)$ 是环境温度
- $f(L(t), T(t))$ 是调整函数

例如，对于220kV线路，调整函数可表示为：

$$
f(L(t), T(t)) = 1 - 0.2 \cdot \frac{L(t)}{L_{max}} - 0.1 \cdot \max\left(\frac{T(t) - T_{ref}}{T_{max} - T_{ref}}, 0\right)
$$

## 断面风险判断与应急措施

### 风险判断标准

断面 $k$ 在时间 $t$ 的风险状态判断：

1. **预警状态**：当 $Risk_{total,k}(t) > Risk_{threshold,1}$ 或 $LR_k(t) > 80\%$
2. **告警状态**：当 $Risk_{total,k}(t) > Risk_{threshold,2}$ 或 $LR_k(t) > 90\%$
3. **紧急状态**：当 $Risk_{total,k}(t) > Risk_{threshold,3}$ 或 $LR_k(t) > 95\%$
4. **故障状态**：当断面中任一线路跳闸或 $LR_k(t) > 100\%$

### 情况一：原断面维持，调节未故障线路电流

当满足以下条件时，可采用调节措施维持原断面：

$$
\sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_{j,max} \geq P_{k,required}(t)
$$

其中：

- $\delta_j$ 表示线路 $j$ 是否故障（1表示故障，0表示正常）
- $P_{k,required}(t)$ 是断面 $k$ 在时间 $t$ 所需的最小传输能力

计算==属于断面且未故障==的线路的容量总和

当不等式成立时，意味着即使有线路故障，剩余未故障线路的总传输能力仍能满足系统需求，这表明可以通过调整剩余线路的负载分配来维持原有断面结构，无需重新划分网络。

当剩余容量足够时，优先通过调整未故障线路负载来维持系统稳定；只有在剩余容量不足时，才会启动更复杂的系统重构流程。



### 情况二：重新划分断面

当满足以下条件时，需要重新划分断面：

$$
\sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_{j,max} < P_{k,required}(t)
$$

或当调节措施无法在规定时间内完成时。

#### 新断面确定算法

1. **系统分区识别**：
   使用广度优先搜索(BFS)或深度优先搜索(DFS)算法，识别网络中断开后形成的孤岛。

   定义连接矩阵 $\mathbf{C}$，其中 $c_{ij} = 1$ 表示节点 $i$ 和节点 $j$ 之间有连接。

2. **关键节点识别**：
   使用图论中的割点算法识别系统中的关键节点。

   节点的重要性可用以下公式计算：
   $$I_i = \sum_{j=1}^{n} \frac{P_j}{d_{ij}}$$
   其中，$P_j$ 是节点 $j$ 的功率，$d_{ij}$ 是节点 $i$ 到节点 $j$ 的距离。

3. **新断面形成**：
   将网络划分为具有最小功率不平衡的子网络。

   子网络功率平衡度：
   $$Balance_k = \left|\frac{\sum_{i \in N_k} P_{gen,i} - \sum_{i \in N_k} P_{load,i}}{\sum_{i \in N_k} P_{load,i}}\right|$$
   其中，$N_k$ 是子网络 $k$ 中的节点集合。

## 调度模块

### 功率重分配算法

当断面中的某条线路发生故障（如跳闸）时，需要重新分配功率流以维持系统平衡。功率重分配算法如下：

#### 线路功率调整算法

当断面中的线路 $j_0$ 发生故障或过载时，其传输的功率 $P_{j_0}(t)$ 需要重新分配到剩余未故障线路。对于每条未故障线路 $j$，新的功率分配为：

$$
P_{j,new}(t) = P_j(t) + \Delta P_j(t)
$$

其中，功率增量 $\Delta P_j(t)$ 可以通过以下方式计算：

$$
\Delta P_j(t) = \frac{(P_{j,max} - P_j(t)) \cdot s_{kj} \cdot (1-\delta_j)}{\sum_{l=1}^{n} (P_{l,max} - P_l(t)) \cdot s_{kl} \cdot (1-\delta_l)} \cdot P_{j_0}(t)
$$

这种分配方式考虑了每条线路的剩余容量，确保功率分配更加合理。

$\Delta P_j(t)$ 表示分配给线路 $j$ 的额外功率，其中：

- $(P_{j,max} - P_j(t))$ 是线路 $j$ 的剩余容量

- $s_{kj}$ 表示线路 $j$ 是否属于断面 $k$（1表示属于，0表示不属于）

- $(1-\delta_j)$ 表示线路 $j$ 是否正常运行（1表示正常，0表示故障）

- $P_{j_0}(t)$ 是故障线路原本传输的功率，需要重新分配

分子：只有属于断面且未故障的线路才会参与分配，且分配比例与其剩余容量成正比。

分母：所有未故障线路剩余容量的总和，确保分配比例合理。

公式按照每条未故障线路的剩余容量比例，将故障线路的功率重新分配出去，考虑了线路是否属于当前断面以及是否处于正常运行状态。

### 备用电源接入策略

当原断面无法满足所需传输能力时，需要接入备用电源。

#### 备用电源选择

备用电源的选择基于以下优先级函数：

$$
Priority(i) = w_a \cdot \frac{P_{avail,i}}{P_{max,i}} + w_b \cdot \frac{1}{d_i} + w_c \cdot \frac{1}{C_i}
$$

其中：

- $P_{avail,i}$ 是备用电源 $i$ 的可用容量
- $P_{max,i}$ 是备用电源 $i$ 的最大容量
- $d_i$ 是备用电源 $i$ 到负荷中心的电气距离
- $C_i$ 是启动备用电源 $i$ 的成本
- $w_a$, $w_b$, $w_c$ 是权重系数，满足 $w_a + w_b + w_c = 1$

#### 备用电源接入流程

1. **容量缺口计算**：
   $$
   P_{deficit}(t) = P_{k,required}(t) - \sum_{j=1}^{n} s_{kj} \cdot (1-\delta_j) \cdot P_j(t)
   $$

2. **备用电源选择**：
   选择优先级最高的备用电源集合 $B$，使得：
   $$
   \sum_{i \in B} P_{avail,i} \geq P_{deficit}(t)
   $$

3. **新断面形成**：
   将选中的备用电源线路加入到断面矩阵中：
   $$
   s_{k,new,j} = 
   \begin{cases}
   s_{kj}, & \text{对于原有线路} \\
   1, & \text{对于新接入的备用电源线路} \\
   0, & \text{其他情况}
   \end{cases}
   $$

### 应急响应流程

#### 案例分析：线路跳闸场景

以一个四线路断面为例（如题干所述的L1、L2、L3、L4组成的断面）：

![断面示例图1](C:\Users\<USER>\Desktop\实习\源网荷储 - 副本\源网荷储 - 副本\建模\断面示例图1.jpg)

1. 初始状态：

   - 每条线路传输功率：$P_j(t) = 7 \text{ MW}$
   - 断面总传输功率：$P_{k,actual}(t) = 28 \text{ MW}$
   - 每条线路最大容量：$P_{j,max} = 10 \text{ MW}$

2. L1跳闸后：

   - 剩余线路最大容量总和：$3 \times 10 = 30 \text{ MW}$
   - 所需传输功率：$P_{k,required}(t) = 28 \text{ MW}$
   - 容量比率：$CapacityRatio = 30/28 > 1$

3. 功率重分配：

   - 每条剩余线路新功率：$P_{j,new}(t) = 28/3 \approx 9.33 \text{ MW}$
   - 每条线路的功率增量：$\Delta P_j(t) = 9.33 - 7 = 2.33 \text{ MW}$

   ![断面示例图2](C:\Users\<USER>\Desktop\实习\源网荷储 - 副本\源网荷储 - 副本\建模\断面示例图2.jpg)

4. 如果剩余线路容量不足（例如只能提供20 MW），则：

   - 容量缺口：$P_{deficit}(t) = 28 - 20 = 8 \text{ MW}$
   - 需要接入备用电源（如L5）提供8 MW功率
   - 形成新断面：L2、L3、L4、L5

通过以上调度模块，系统可以在线路故障情况下快速响应，维持电网稳定运行。

---

## 故障链分析框架

### 第一阶段：极端天气→可再生能源功率波动

#### 子阶段A：寒潮→O1.1-大型风电场功率波动

1. **关键过程**：
   - 寒潮天气条件导致风机性能下降或停机
   - O1.1-大型风电场出力大幅波动

2. **定量关系**：
   - 寒潮强度 $C_j^W(t)$ 与风机影响程度 $I_{ij}^W(t)$ 的映射
   - 风电场功率变化 $P_i^W - P_i^{W'}(t)$ 与爬坡率 $r(t_1, t_2)$ 的计算
   - 各风电场影响程度差异：基于风电场的地理分布和风机特性，不同风电场受影响的程度和时序不同

3. **风电场级联效应**：
   - 广州市O1.1大型风电场群由多个分场站组成，每个风电场对应不同的输电线路
   - 寒潮影响可能导致多个风电场同时或先后减少输出功率
   - 风电场总功率变化：$\Delta P_{total}^W(t) = \sum_{i=1}^{n_W} [P_i^W - P_i^{W'}(t)]$

#### 子阶段B：雷击→光伏电站功率波动

1. **关键过程**：
   - 雷击事件导致光伏电站组件损坏或保护性停机
   - 光伏电站输出功率急剧下降

2. **定量关系**：
   - 雷击强度 $C_k^{PV}(t)$ 与光伏电站影响程度 $I_{ik}^{PV}(t)$ 的映射
   - 光伏电站功率变化 $P_j^{PV} - P_j^{PV'}(t)$ 对系统的冲击
   - 不同光伏电站受影响差异：基于电站地理位置、防雷设施和接线方式

3. **光伏电站失效特性**：
   - 雷击导致的光伏电站失效通常具有突发性（相比寒潮的渐进性）
   - 可能出现部分组串失效而非整站停机
   - 光伏电站总功率变化：$\Delta P_{total}^{PV}(t) = \sum_{j=1}^{n_{PV}} [P_j^{PV} - P_j^{PV'}(t)]$

#### 总体影响：爬坡事件形成

1. **综合功率波动**：
   - 风电和光伏共同贡献的总功率：$P_{total}(t) = \sum_{i=1}^{n_W} P_i^{W'}(t) + \sum_{j=1}^{n_{PV}} P_j^{PV'}(t)$
   - 总爬坡率计算：$r(t_1, t_2) = \frac{|P_{total}(t_2) - P_{total}(t_1)|}{t_2 - t_1}$
   - 当 $r(t_1, t_2) > r_{thres}$ 时，形成爬坡事件

2. **爬坡叠加效应**：
   - 寒潮和雷击在时间上可能同时或先后发生
   - 同时发生时，两种影响叠加会使爬坡率更高
   - 先后发生时，可能导致连续的多次爬坡事件

### 第二阶段：可再生能源功率波动→E1-失压或过载

1. **关键过程**：
   - 风电场和光伏电站功率波动通过输电线路传导至电网关键节点
   - 当电压低于限值或线路负载率超过100%时，出现E1故障

2. **定量关系**：
   - 可再生能源与输电线路的关联：$M_{il}^W$ 和 $M_{jl}^{PV}$ 矩阵确定功率流向
   - 各线路负载分配：
     - 风电贡献：$P_{l,wind}(t) = \sum_{i=1}^{n_W} M_{il}^W \cdot P_i^{W'}(t) \cdot PTDF_{i,l}^{W,P}$
     - 光伏贡献：$P_{l,pv}(t) = \sum_{j=1}^{n_{PV}} M_{jl}^{PV} \cdot P_j^{PV'}(t) \cdot PTDF_{j,l}^{PV,P}$
   - 输电线路负载率：$LR_l(t) = \frac{S_{line,l}(t)}{S_{rated,l}} \times 100\%$
   - 关键节点电压变化：$V_{bus,k}(t) = V_{bus,k}(t_0) + \Delta V_{bus,k}(t)$

3. **线路差异性影响**：
   - 每条输电线路的额定容量、物理特性和拓扑位置不同
   - 功率分配不均：部分线路可能超载而其他线路仍有余量
   - 负载率差异计算：$\Delta LR(t) = \max\limits_{l \in L} LR_l(t) - \min\limits_{l \in L} LR_l(t)$

4. **风电与光伏影响差异**：
   - 风电场和光伏电站通常位于不同地理位置，连接不同输电线路
   - 功率特性差异：风电具有较大的无功功率波动，而光伏主要是有功功率变化
   - 时间特性差异：风电受寒潮影响较持续，光伏受雷击影响较突发

---

## 模型应用流程

1. 收集气象数据（温度、风速、风向、持续时间等）  
2. 获取风机与电力系统参数（位置、型号、网络拓扑等）  
3. 计算寒潮强度 $C_j^W(t)$ 及其对风机的影响程度 $I_{ij}^W(t)$  
4. 评估风机受影响后的发电功率 $P_i^{W'}(t)$  
5. 计算爬坡率 $r(t_1, t_2)$ 并评估爬坡风险  
6. 分析风电变化对系统节点电压和线路负载的影响  
7. 计算E1故障(失压或过载)发生概率 $E1_{prob}(t)$  
8. 评估O3-220KV线路受影响概率 $O3_{prob}(t)$  
9. 统计整体影响 $I_{\text{total}}^W$，并输出风机风险评分 $R_i^W$  
10. 为电力系统调度与安全管理提供预警与优化建议  

---

## 模型结构图

+---------------------------------------------------------------------+

| 输入数据                                                     |
| ------------------------------------------------------------ |
| - 气象数据：                                                 |
| - 寒潮：温度降幅 $T_j(t)$、风速 $W_j(t)$、持续时间 $D_j(t)$  |
| - 雷击：雷电流强度 $L_k(t)$、频次 $F_k(t)$、影响面积 $A_k(t)$ |
| - 设备参数：                                                 |
| - 风机：位置 $(x_i^W, y_i^W)$、高度 $H_i$、使用年限 $A_i$、型号参数 $M_i$ |
| - 光伏：位置 $(x_i^{PV}, y_i^{PV})$、防雷等级 $LP_i$、接线方式 $DS_i$、使用年限 $A_i^{PV}$ |
| - 电力系统参数：网络拓扑、线路参数、节点电压限值、线路容量   |
| - 实时数据：风向、实时辐照度                                 |
| - 系统参数：爬坡率阈值 $r_{thres}$、常规机组爬坡能力         |
| - **线路参数**：O3-220KV线路系统组成 $O3_{total} = \{l_1, l_2, ..., l_q\}$ |
| - **电源-线路对应关系**：                                    |
| - 风电场-线路矩阵 $M_{il}^W$ 描述风电场 $i$ 与线路 $l$ 的拓扑连接 |
| - 光伏电站-线路矩阵 $M_{jl}^{PV}$ 描述光伏电站 $j$ 与线路 $l$ 的拓扑连接 |
| +--------------------------------↓------------------------------------+ |

+---------------------------------------------------------------------+
|                          第一阶段：极端天气→可再生能源功率波动       |
| A. 寒潮→风电场：                                                   |
| 1. 寒潮强度计算：$C_j^W(t)$                                        |
| 2. 风机空间修正与敏感度计算                                        |
| 3. 风机影响程度 $I_{ij}^W(t)$ 计算                                 |
| 4. 风机功率变化 $P_i^{W'}(t)$ 计算                                 |
|                                                                    |
| B. 雷击→光伏电站：                                                 |
| 1. 雷击强度计算：$C_k^{PV}(t)$                                     |
| 2. 光伏电站敏感度 $S_i^{PV}$ 计算                                  |
| 3. 光伏电站影响程度 $I_{ik}^{PV}(t)$ 计算                          |
| 4. 光伏电站功率变化 $P_j^{PV'}(t)$ 计算                            |
|                                                                    |
| C. 台风→风机和光伏系统功率波动：                                   |
| 1. 台风强度模型：$C_j^T(t)$                                       |
| 2. 距离计算模型：$d_{ij}$                                          |
| 3. 台风距离衰减函数：$f_T(d_{ij})$                                 |
| 4. 台风方向修正函数：$g_T(\theta_{ij}^T)$                          |
| 5. 风机台风敏感度模型：$S_i^{WT}$                                  |
| 6. 台风对风机影响程度模型：$I_{ij}^{WT}(t)$                        |
| 7. 风机物理损坏概率模型：$PD_i^{WT}(t)$                             |
| 8. 台风影响下风机功率模型：$P_i^{W'T}(t)$                           |
| 9. 光伏系统台风敏感度模型：$S_i^{PVT}$                             |
| 10. 台风条件下光照因子模型：$LF_i(t)$                              |
| 11. 台风对光伏系统影响程度模型：$I_{ij}^{PVT}(t)$                   |
| 12. 光伏系统物理损坏概率模型：$PD_i^{PVT}(t)$                       |
| 13. 台风影响下光伏系统功率模型：$P_i^{PV'T}(t)$                     |
|                                                                    |
| D. 综合计算：                                                      |
| 1. 总功率计算：$P_{total}(t) = \sum_{i=1}^{n_W} P_i^{W'}(t) + \sum_{j=1}^{n_{PV}} P_j^{PV'}(t)$ |
| 2. 爬坡率 $r(t_1, t_2)$ 评估                                       |
+--------------------------------↓------------------------------------+

+---------------------------------------------------------------------+
|                          第二阶段：可再生能源功率波动→E1故障       |
| 1. 功率传输映射：                                                  |
|    - 风电场：$P_{l,wind}(t) = \sum_{i=1}^{n_W} M_{il}^W \cdot P_i^{W'}(t) \cdot PTDF_{i,l}^{W,P}$ |
|    - 光伏电站：$P_{l,pv}(t) = \sum_{j=1}^{n_{PV}} M_{jl}^{PV} \cdot P_j^{PV'}(t) \cdot PTDF_{j,l}^{PV,P}$ |
| 2. 各线路视在功率计算：$S_{line,l}(t) = \sqrt{(...)}$              |
| 3. 线路负载率计算：$LR_l(t) = \frac{S_{line,l}(t)}{S_{rated,l}} \times 100\%$ |
| 4. 电压响应计算：$V_{bus,k}(t) = V_{bus,k}(t_0) + \Delta V_{bus,k}(t)$ |
| 5. E1故障概率评估：$E1_{prob}(t)$                                 |
| 6. 线路风险与爬坡风险关联分析：$Corr_{l}(Risk_{ramp}, Risk_{line})$ |
+--------------------------------↓------------------------------------+

+---------------------------------------------------------------------+
|                          第三阶段：E1故障→O3-220KV线路             |
| 1. 分线路影响概率计算：$O3_{prob,l}(t) = E1_{prob}(t) \cdot SIF_{O3,l}(t)$ |
| 2. 总线路功率汇总：$S_{O3,total}(t) = \sum_{l \in O3_{total}} S_{line,l}(t)$ |
| 3. 功率分配比例计算：$Ratio_l(t) = \frac{S_{line,l}(t)}{S_{O3,total}(t)} \times 100\%$ |
| 4. 系统断面形成概率评估：$Section_{prob}(t) = 1 - \prod_{l \in O3_{critical}} (1 - O3_{prob,l}(t))$ |
+--------------------------------↓------------------------------------+

+---------------------------------------------------------------------+
|                          综合风险评估                              |
| 1. 爬坡风险评级：$Risk_{ramp}(t_1, t_2)$                          |
| 2. 线路风险评级：$Risk_{line,l}(t)$                                |
| 3. 整体影响程度：                                                  |
|    - 风电系统：$I_{\text{total}}^W$                                |
|    - 光伏系统：$I_{\text{total}}^{PV}$                              |
| 4. 系统断面形成风险：$Section_{prob}(t)$                           |
+--------------------------------↓------------------------------------+

+---------------------------------------------------------------------+
| 输出结果                                                           |
| 1. 可再生能源功率预测曲线：                                        |
|    - 分风电场功率预测                                              |
|    - 分光伏电站功率预测                                            |
| 2. 各输电线路负载率预测曲线（分线路）                              |
| 3. 爬坡事件风险预警                                               |
| 4. E1故障概率预测                                                 |
| 5. O3-220KV线路系统影响风险评估                                    |
| 6. 系统断面形成风险预警                                           |
| 7. 分级防控措施建议                                               |
+---------------------------------------------------------------------+

### 光伏电站整体影响程度

$$
I_{\text{total}}^{PV} = \frac{\sum_{j=1}^{n_{PV}} P_j^{PV} - \sum_{j=1}^{n_{PV}} P_j^{PV'}(t)}{\sum_{j=1}^{n_{PV}} P_j^{PV}} \times 100\%
$$

- $P_j^{PV}$：光伏电站 $j$ 的额定出力。
- $P_j^{PV'}(t)$：光伏电站 $j$ 受雷击影响后的实际出力。
- $I_{\text{total}}^{PV}$：全市光伏电站总损失占比。

**建模理由**：

- 量化雷击事件对全市光伏系统的整体冲击。
- 便于横向对比不同雷击事件或不同防控措施下的系统损失。
- 与风电系统损失指标保持一致，便于比较两种可再生能源的受影响程度。

**工程意义**：

- 为光伏电站调度和应急响应提供依据。
- 支持光伏系统韧性评估和防雷设施优化。

---

### 光伏电站风险评分

$$
R_j^{PV} = I_{jk}^{PV}(t) \cdot P_j^{PV}
$$

- $I_{jk}^{PV}(t)$：雷击 $k$ 在时间 $t$ 对光伏电站 $j$ 的影响程度。
- $P_j^{PV}$：光伏电站 $j$ 的额定功率。
- $R_j^{PV}$：光伏电站的风险评分。

**建模理由**：

- 综合考虑光伏电站受雷击影响的严重程度和容量，量化各电站风险。
- 便于排序和优先调度高风险区域的光伏电站。

**工程意义**：

- 支持光伏电站差异化防控策略制定。
- 为防雷设施加强和升级提供决策依据。

### 多灾害影响融合模型分步推导

==非独立伯努利事件的幂表达近似==
$$
P_i^{PV'multi}(t) = P_i^{PV} \cdot \left[ 1 - \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}} \right]
$$

#### 分别计算"未受影响的比例"

雷击未影响部分：
$$
1 - I_{ik}^{PV}(t)
$$

台风未影响部分：
$$
1 - I_{ij}^{PVT}(t)
$$

---

#### 加权幂次，反映灾害重要性（广义幂函数表达）

雷击未影响的权重：
$$
\left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}}
$$

台风未影响的权重：
$$
\left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}}
$$

---

#### 乘积，反映协同未受影响部分（非独立伯努利事件的幂表达近似）

两灾害共同作用下的剩余可用比例（假设联合分布符合联合概率服从如下分布，a=b=1代表独立事件，如果 $α≠1$ 或 $β≠1\$ ，则为非独立建模）
$$
\left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}}
$$

---

#### 1减去乘积，得到"总损失比例"

总损失比例（未考虑协同效应非线性）：
$$
1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right]
$$

---

#### 再用 $1/\gamma_{PV}$ 次方，调整协同效应强弱（==非独立伯努利事件的幂表达近似==）

![image-20250601185738466](C:\Users\<USER>\Desktop\实习\源网荷储 - 副本\源网荷储 - 副本\建模\image-20250601185738466.png)

![image-20250601190550885](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601190550885.png)

![image-20250601192024135](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601192024135.png)

![image-20250620173216773](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250620173216773.png)

![image-20250601192058870](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601192058870.png)

![image-20250601192142595](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601192142595.png)

协同作用因子调整后：
$$
\left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}}
$$

---

#### 最外层再用 1 减去，得到最终"未受影响比例"

最终损失比例：
$$
1 - \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}}
$$

---

#### 乘以额定功率，得到实际输出功率

多灾害影响下的实际输出功率：
$$
P_i^{PV'multi}(t) = P_i^{PV} \cdot \left[ 1 - \left\{ 1 - \left[ \left(1 - I_{ik}^{PV}(t)\right)^{\alpha_{PV}} \cdot \left(1 - I_{ij}^{PVT}(t)\right)^{\beta_{PV}} \right] \right\}^{1/\gamma_{PV}} \right]
$$
![image-20250601193943049](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601193943049.png)



![image-20250601233505117](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233505117.png)

![image-20250601233526206](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233526206.png)

![image-20250601233543553](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233543553.png)

![image-20250601233557890](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233557890.png)

![image-20250601233622111](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233622111.png)

![image-20250601233638054](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233638054.png)

![image-20250601233649324](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233649324.png)

![image-20250601233659706](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250601233659706.png)