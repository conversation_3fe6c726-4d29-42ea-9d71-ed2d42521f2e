# 寒潮对广州市大型风机影响及电力系统连锁故障建模 - 数据需求文档

## 文档概述

本文档基于《寒潮对广州市大型风机影响及电力系统连锁故障的数学建模》项目，详细列出各个建模环节中需要电力局提供的数据以及需要电力局确认的关键参数和阈值。

---

## 一、基础地理和设施数据需求

### 1.1 风电场基础数据
**数据提供方：电力局**

| 数据项 | 具体要求 | 用途 |
|--------|----------|------|
| 风电场地理坐标 | 各风电场的精确经纬度坐标 $(x_i^W, y_i^W)$ | 计算与寒潮中心的距离 |
| 风机技术参数 | 安装高度 $H_i$、额定风速 $V_{r_i}$、耐寒温度指标 $T_{lim_i}$ | 风机敏感度建模 |
| 风机使用年限 | 各风电场风机的使用年限 $A_i$ | 敏感度系数计算 |
| 风机型号参数 | 切出风速、抗台风设计等级、偏航系统性能 | 台风敏感度建模 |
| 风电场装机容量 | 各风电场的总装机容量和单机容量 | 功率计算和调度策略 |

### 1.2 光伏电站基础数据
**数据提供方：电力局**

| 数据项 | 具体要求 | 用途 |
|--------|----------|------|
| 光伏电站地理坐标 | 各光伏电站的精确经纬度坐标 $(x_i^{PV}, y_i^{PV})$ | 计算与雷击中心的距离 |
| 防雷保护等级 | 各光伏电站的防雷保护等级 $LP_i$ | 雷击敏感度建模 |
| 接线方式 | 电站接线方式 $DS_i$ | 雷击影响评估 |
| 技术类型 | 组件类型（单晶硅、多晶硅、薄膜等）$T_i^{PV}$ | 敏感度系数计算 |
| 安装方式 | 地面固定式、屋顶平铺式、水面漂浮式等 $MS_i$ | 台风敏感度建模 |
| 使用年限 | 各光伏电站的使用年限 $A_i^{PV}$ | 敏感度系数计算 |

### 1.3 电网拓扑和线路数据
**数据提供方：电力局**

| 数据项 | 具体要求 | 用途 |
|--------|----------|------|
| 电网拓扑结构 | 节点连接关系、线路编号 | PTDF矩阵计算 |
| 线路技术参数 | 电阻、电抗、电容、额定容量 $S_{rated,l}$ | 潮流计算和负载率分析 |
| 220kV线路清单 | O3-220kV线路系统的具体线路信息 | 连锁故障分析 |
| 变电站信息 | 变电站位置、容量、电压等级 | 系统建模 |
| 连接矩阵 | 风电场/光伏电站与线路的连接关系 $M_{il}^W$, $M_{il}^{PV}$ | 功率传输分析 |

---

## 二、历史运行数据需求

### 2.1 气象灾害历史数据
**数据提供方：电力局（与气象部门协调获取）**

| 数据项 | 时间范围 | 数据频率 | 用途 |
|--------|----------|----------|------|
| 寒潮事件记录 | 近10年 | 逐小时 | 寒潮强度模型训练 |
| 温度数据 | 近10年 | 逐小时 | 温度降幅计算 $T_j$ |
| 风速风向数据 | 近10年 | 逐小时 | 风向修正函数 $g(\theta_{ij})$ |
| 雷击事件记录 | 近10年 | 逐次 | 雷击强度模型训练 |
| 台风路径数据 | 近10年 | 逐6小时 | 台风影响建模 |

### 2.2 设备运行历史数据
**数据提供方：电力局**

| 数据项 | 时间范围 | 数据频率 | 用途 |
|--------|----------|----------|------|
| 风电场出力数据 | 近5年 | 15分钟 | 功率波动分析 |
| 光伏电站出力数据 | 近5年 | 15分钟 | 功率波动分析 |
| 线路负载率数据 | 近5年 | 15分钟 | 负载率模型训练 |
| 故障记录 | 近10年 | 逐次 | 故障概率建模 |
| 设备状态记录 | 近5年 | 逐日 | 状态转移概率估计 |

### 2.3 系统响应数据
**数据提供方：电力局**

| 数据项 | 具体要求 | 用途 |
|--------|----------|------|
| 爬坡事件记录 | 历史爬坡事件的时间、幅度、持续时间 | 爬坡率阈值确定 |
| 调度记录 | 历史调度指令和执行效果 | 调度策略优化 |
| 连锁故障案例 | 历史连锁故障的发展过程和影响范围 | 连锁故障模型验证 |

---

## 三、需要电力局确认的关键参数

### 3.1 风险评估阈值确认
**确认方：电力局运行部门**

| 参数类别 | 参数名称 | 建议值 | 需确认内容 |
|----------|----------|--------|------------|
| 负载率风险阈值 | 低风险 | ≤70% | 是否符合实际运行标准 |
| | 中等风险 | 70%-85% | 阈值划分是否合理 |
| | 高风险 | 85%-95% | 是否需要调整 |
| | 极高风险 | >95% | 紧急状态触发点 |
| 爬坡率阈值 | $r_{thres}$ | 待确认 | 系统可承受的最大爬坡率 |
| 安全裕度系数 | $SF$ | 1.1 | 安全裕度是否充足 |

### 3.2 故障概率模型参数确认
**确认方：电力局技术部门**

| 参数类别 | 参数名称 | 需确认内容 |
|----------|----------|------------|
| E1故障阈值 | 失压阈值 | 节点最低允许电压 $V_{min,k}$ |
| | 过载阈值 | 线路过载保护动作值 |
| O3线路风险 | 关键线路识别 | 哪些220kV线路属于关键线路 |
| | 风险传递系数 | 爬坡风险向线路风险的转移概率 |

### 3.3 调度策略参数确认
**确认方：电力局调度中心**

| 参数类别 | 参数名称 | 需确认内容 |
|----------|----------|------------|
| 调度优先级 | 风电场调整系数 $\alpha_s$ | 各风电场的调度优先级 |
| | 光伏电站调整系数 | 各光伏电站的调度优先级 |
| PTDF阈值 | 筛选阈值 $\varepsilon$ | 参与调度的最小PTDF值 |
| 安全负载率 | $LR_{safe}$ | 调度目标负载率（建议95%） |

---

## 四、实时数据接口需求

### 4.1 实时监测数据
**数据提供方：电力局SCADA系统**

| 数据项 | 更新频率 | 数据格式 | 用途 |
|--------|----------|----------|------|
| 实时功率数据 | 1分钟 | 数值型 | 实时状态评估 |
| 线路负载率 | 1分钟 | 百分比 | 过载监测 |
| 节点电压 | 1分钟 | 标幺值 | 电压稳定监测 |
| 开关状态 | 实时 | 布尔型 | 拓扑变化监测 |

### 4.2 气象预报数据
**数据提供方：电力局（与气象部门对接）**

| 数据项 | 预报时长 | 更新频率 | 用途 |
|--------|----------|----------|------|
| 温度预报 | 72小时 | 3小时 | 寒潮预警 |
| 风速风向预报 | 72小时 | 3小时 | 风向影响预测 |
| 雷电预警 | 24小时 | 1小时 | 雷击风险预警 |
| 台风路径预报 | 120小时 | 6小时 | 台风影响预测 |

---

## 五、模型参数标定需求

### 5.1 敏感度系数标定
**协作方：电力局技术专家**

| 系数类别 | 需要标定的参数 | 标定方法 |
|----------|----------------|----------|
| 寒潮影响系数 | $\alpha, \beta, \gamma$ | 基于历史数据回归分析 |
| 风向修正系数 | 风向影响权重 | 现场测试数据验证 |
| 距离衰减系数 | $\lambda, d_0$ | 多点测量数据拟合 |
| 敏感度权重 | $\delta_1, \delta_2, \delta_3, \delta_4, \delta_5$ | 专家经验结合数据分析 |

### 5.2 概率模型参数标定
**协作方：电力局运行专家**

| 模型类别 | 需要标定的参数 | 标定依据 |
|----------|----------------|----------|
| 马尔可夫模型 | 状态转移概率矩阵 | 历史状态序列数据 |
| 混合模型 | 权重函数参数 $\alpha_k, \beta_k, \gamma_k$ | EM算法训练 |
| 故障概率模型 | 故障率参数 | 历史故障统计 |

---

## 六、数据质量要求

### 6.1 数据完整性要求
- 历史数据缺失率不超过5%
- 关键参数（如装机容量、线路参数）必须100%完整
- 实时数据可用率不低于99.5%

### 6.2 数据准确性要求
- 地理坐标精度：小数点后6位（约1米精度）
- 功率数据精度：±1%
- 时间戳精度：秒级同步

### 6.3 数据更新要求
- 基础数据：年度更新
- 设备参数：设备变更时及时更新
- 实时数据：按指定频率持续更新

---

## 七、数据安全和保密要求

### 7.1 数据分级
- **公开级**：一般性技术参数
- **内部级**：详细运行数据
- **机密级**：关键设施位置和容量信息

### 7.2 数据使用约定
- 数据仅用于本项目建模研究
- 不得向第三方泄露敏感信息
- 研究结果需经电力局审核后发布

---

## 八、数据交付时间安排

### 8.1 第一阶段（项目启动后1个月内）
- 基础设施数据
- 电网拓扑数据
- 近3年历史运行数据

### 8.2 第二阶段（项目启动后2个月内）
- 完整历史数据（5-10年）
- 详细设备参数
- 实时数据接口开通

### 8.3 第三阶段（项目启动后3个月内）
- 参数确认完成
- 模型验证数据
- 专家评审意见

---

## 九、联系方式和责任分工

### 9.1 数据提供责任人
- **运行数据**：电力局运行部门
- **设备数据**：电力局技术部门  
- **调度数据**：电力局调度中心
- **气象数据**：电力局与气象部门协调

### 9.2 技术对接联系人
- **项目总协调**：[待指定]
- **数据技术对接**：[待指定]
- **模型验证专家**：[待指定]

---

## 十、备注说明

1. 本文档中标记"待确认"的参数需要电力局专家根据实际运行经验确定
2. 部分敏感数据可采用脱敏处理，保留建模所需的统计特征
3. 如有数据获取困难，可协商采用替代方案或简化模型
4. 数据格式和接口协议需要进一步技术对接确定

**文档版本**：V1.0  
**编制日期**：2024年  
**更新记录**：初始版本
